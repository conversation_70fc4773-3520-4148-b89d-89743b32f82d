import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { AiFacade } from '@venio/data-access/ai'
import { BehaviorSubject } from 'rxjs'

import { ListOfFilesComponent } from './list-of-files.component'

describe('ListOfFilesComponent', () => {
  let component: ListOfFilesComponent
  let fixture: ComponentFixture<ListOfFilesComponent>

  const mockAiFacade = {
    selectEciFileData$: new BehaviorSubject([]),
    selectEciPagedFileData$: new BehaviorSubject([]),
    selectActiveChartType$: new BehaviorSubject(null),
    selectChartSelectedNode$: jest
      .fn()
      .mockReturnValue(new BehaviorSubject(null)),
    selectEciPaginationState$: new BehaviorSubject({
      currentPage: 1,
      pageSize: 15,
      totalRecords: 0,
    }),
    updateEciPagination: jest.fn(),
  } satisfies Partial<AiFacade>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ListOfFilesComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimations(),
        { provide: AiFacade, useValue: mockAiFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ListOfFilesComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
