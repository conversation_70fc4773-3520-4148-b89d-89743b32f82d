import {
  AfterViewInit,
  Component,
  inject,
  computed,
  effect,
  input,
  signal,
  PLATFORM_ID,
} from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { calculateSunburstData } from './helpers'

import { isPlatformBrowser, NgIf } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'
import { relevanceChartColors, otherChartColors } from '../../constants/colors'
import {
  formatParentData,
  formatChildData,
} from '../data-table-for-focused-section/helpers'
import { CenterTextComponent } from '../center-text/center-text.component'

@Component({
  selector: 'venio-focused-sunburst',
  standalone: true,
  imports: [PlotlyModule, CenterTextComponent, NgIf],
  templateUrl: './focused-sunburst.component.html',
  styleUrl: './focused-sunburst.component.scss',
})
export class FocusedSunburstComponent implements AfterViewInit {
  public readonly chartTitle = input<SunburstChartType>()

  private readonly aiFacade = inject(AiFacade)

  private readonly platformId = inject(PLATFORM_ID)

  private readonly isBrowser = isPlatformBrowser(this.platformId)

  private readonly SunburstChartType = SunburstChartType

  // ECA API data - Document Types
  public readonly ecaDocumentTypesData = toSignal(
    this.aiFacade.selectEcaDocumentTypesSuccess$,
    { initialValue: null }
  )

  // ECA API data - Topics Relevant
  public readonly ecaTopicsRelevantData = toSignal(
    this.aiFacade.selectEcaTopicsRelevantSuccess$,
    { initialValue: null }
  )

  // ECA API data - Topics Non-Relevant
  public readonly ecaTopicsNonRelevantData = toSignal(
    this.aiFacade.selectEcaTopicsNonRelevantSuccess$,
    { initialValue: null }
  )

  // ECA API data - Relevance
  public readonly ecaRelevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  // Loading state signals for all chart types
  public readonly isEcaDocumentTypesLoading = toSignal(
    this.aiFacade.selectIsEcaDocumentTypesLoading$,
    { initialValue: false }
  )

  public readonly isEcaTopicsRelevantLoading = toSignal(
    this.aiFacade.selectIsEcaTopicsRelevantLoading$,
    { initialValue: false }
  )

  public readonly isEcaTopicsNonRelevantLoading = toSignal(
    this.aiFacade.selectIsEcaTopicsNonRelevantLoading$,
    { initialValue: false }
  )

  public readonly isEcaRelevanceLoading = toSignal(
    this.aiFacade.selectIsEcaRelevanceLoading$,
    { initialValue: false }
  )

  // Computed signal for current chart data based on chart type
  public readonly currentChartData = computed(() => {
    try {
      const chartTitle = this.chartTitle()
      switch (chartTitle) {
        case this.SunburstChartType.DocumentTypes: {
          const docTypesData = this.ecaDocumentTypesData()
          if (
            docTypesData?.data?.documentTypes &&
            Array.isArray(docTypesData.data.documentTypes) &&
            docTypesData.data.documentTypes.length > 0
          ) {
            return this.#transformEcaDocumentTypes(
              docTypesData.data.documentTypes
            )
          }
          return []
        }

        case this.SunburstChartType.RelevantDocuments: {
          const relevantData = this.ecaTopicsRelevantData()
          if (
            relevantData?.data?.topics &&
            Array.isArray(relevantData.data.topics) &&
            relevantData.data.topics.length > 0
          ) {
            return this.#transformEcaTopicsToDocumentTypes(
              relevantData.data.topics
            )
          }
          return []
        }

        case this.SunburstChartType.NotRelevantDocuments: {
          const nonRelevantData = this.ecaTopicsNonRelevantData()
          if (
            nonRelevantData?.data?.topics &&
            Array.isArray(nonRelevantData.data.topics) &&
            nonRelevantData.data.topics.length > 0
          ) {
            return this.#transformEcaTopicsToDocumentTypes(
              nonRelevantData.data.topics
            )
          }
          return []
        }

        case this.SunburstChartType.Relevance: {
          // For relevance chart, use actual relevance data
          const relevanceData = this.ecaRelevanceData()

          if (
            relevanceData?.data &&
            Array.isArray(relevanceData.data) &&
            relevanceData.data.length > 0
          ) {
            return this.#transformEcaRelevanceData(relevanceData.data)
          }
          return []
        }

        default:
          return []
      }
    } catch (error) {
      console.error('Error computing current chart data:', error)
      return []
    }
  })

  // Chart-specific state signals for all chart types (created outside reactive context)
  public readonly documentTypesStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(
      this.SunburstChartType.DocumentTypes
    ),
    { initialValue: null }
  )

  public readonly relevantDocumentsStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(
      this.SunburstChartType.RelevantDocuments
    ),
    { initialValue: null }
  )

  public readonly notRelevantDocumentsStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(
      this.SunburstChartType.NotRelevantDocuments
    ),
    { initialValue: null }
  )

  public readonly relevanceStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(this.SunburstChartType.Relevance),
    { initialValue: null }
  )

  // Chart-specific can go back signals for all chart types
  public readonly documentTypesCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(this.SunburstChartType.DocumentTypes),
    { initialValue: false }
  )

  public readonly relevantDocumentsCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(
      this.SunburstChartType.RelevantDocuments
    ),
    { initialValue: false }
  )

  public readonly notRelevantDocumentsCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(
      this.SunburstChartType.NotRelevantDocuments
    ),
    { initialValue: false }
  )

  public readonly relevanceCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(this.SunburstChartType.Relevance),
    { initialValue: false }
  )

  // Computed properties that select the correct state based on chart type (always in focused mode)
  public readonly chartDrillDownState = computed(() => {
    const chartTitle = this.chartTitle()
    switch (chartTitle) {
      case this.SunburstChartType.DocumentTypes:
        return this.documentTypesStateSignal()
      case this.SunburstChartType.RelevantDocuments:
        return this.relevantDocumentsStateSignal()
      case this.SunburstChartType.NotRelevantDocuments:
        return this.notRelevantDocumentsStateSignal()
      case this.SunburstChartType.Relevance:
        return this.relevanceStateSignal()
      default:
        return this.documentTypesStateSignal()
    }
  })

  public readonly chartIsExpanded = computed(() => {
    const drillDownState = this.chartDrillDownState()
    return drillDownState?.isExpanded || false
  })

  public readonly chartCanGoBack = computed(() => {
    const chartTitle = this.chartTitle()
    console.log('chartCanGoBack:', chartTitle)
    switch (chartTitle) {
      case this.SunburstChartType.DocumentTypes:
        return this.documentTypesCanGoBackSignal()
      case this.SunburstChartType.RelevantDocuments:
        return this.relevantDocumentsCanGoBackSignal()
      case this.SunburstChartType.NotRelevantDocuments:
        return this.notRelevantDocumentsCanGoBackSignal()
      case this.SunburstChartType.Relevance:
        return this.relevanceCanGoBackSignal()
      default:
        return this.documentTypesCanGoBackSignal()
    }
  })

  // Computed property to determine colors based on chart type
  public readonly chartColors = computed(() => {
    const chartTitle = this.chartTitle()
    return chartTitle === SunburstChartType.Relevance
      ? relevanceChartColors
      : otherChartColors
  })

  // Signal to determine if current chart type is loading
  public readonly isCurrentChartLoading = computed(() => {
    const chartTitle = this.chartTitle()
    switch (chartTitle) {
      case this.SunburstChartType.DocumentTypes:
        return this.isEcaDocumentTypesLoading()
      case this.SunburstChartType.RelevantDocuments:
        return this.isEcaTopicsRelevantLoading()
      case this.SunburstChartType.NotRelevantDocuments:
        return this.isEcaTopicsNonRelevantLoading()
      case this.SunburstChartType.Relevance:
        return this.isEcaRelevanceLoading()
      default:
        return false
    }
  })

  // Signal to determine if chart data is ready for rendering
  public readonly isChartDataReady = computed(() => {
    const chartData = this.currentChartData()
    const isLoading = this.isCurrentChartLoading()

    // Data is ready if we have data AND we're not currently loading
    return (
      chartData &&
      Array.isArray(chartData) &&
      chartData.length > 0 &&
      !isLoading
    )
  })

  // Signal to determine if chart should be rendered
  public readonly shouldRenderChart = computed(() => {
    return this.isBrowser && this.isChartDataReady()
  })

  // Chart data properties
  public parentLabels: any = []

  public childrenLabels: any = []

  public chartOneTotal = 0

  public chartOneSubTotal: number[] = []

  public chartOnePercents: number[] | undefined = []

  public chartOneChildPercents: number[][] | undefined = []

  public allCountsOne: number[] = []

  public allValsOne: number[] = []

  public formattedCounts: string[] = []

  // Internal state signals for chart data
  private readonly chartDataSignal = signal<any[]>([])

  private readonly chartLayoutSignal = signal<any>({})

  private readonly chartConfigSignal = signal<any>({})

  // Computed signals for reactive Plotly integration
  public readonly graph = computed(() => ({
    data: this.chartDataSignal(),
    layout: this.chartLayoutSignal(),
  }))

  public readonly config = computed(() => this.chartConfigSignal())

  public labels: string[] = []

  public parents: string[] = ['Total']

  // Store original colors for highlighting reset
  private originalColors: string[] = []

  // Store original values for smooth expansion/collapse
  private originalValues: number[] = []

  // Flag to prevent infinite loops during initialization
  #isInitializing = false

  // Track last chart state to prevent unnecessary rebuilds
  private lastChartLevel = 0

  private lastChartExpanded = false

  // Legend color mapping for synchronization with chart colors
  public legendColorMap = new Map<string, string>()

  // State to track legend-triggered modifications
  private readonly isLegendModifiedSignal = signal(false)

  private readonly legendSelectedDataSignal = signal<any>(null)

  // Convert show details observable to signal
  public readonly eciShowDetails = toSignal(
    this.aiFacade.selectEciShowDetails$,
    { initialValue: false }
  )

  constructor() {
    // Track previous chart type for simple change detection
    let previousChartType: SunburstChartType | null = null

    // Simple effect to handle chart type changes and ensure proper reset
    effect(
      () => {
        const currentChartType = this.chartTitle()
        const shouldRender = this.shouldRenderChart()

        // Only reset if we have a previous type and it's different
        if (
          previousChartType &&
          previousChartType !== currentChartType &&
          !this.#isInitializing
        ) {
          console.log(
            'Chart type changed, resetting drill-down state for:',
            currentChartType
          )
          // Reset drill-down state in store
          this.aiFacade.resetChartDrillDown(currentChartType)

          // CRITICAL: Reset legend modification state when switching chart types
          // This prevents state pollution from previous chart type (especially Relevance -> other types)
          this.#setLegendModifiedState(false, null)
          this.legendColorMap.clear()

          console.log(
            `🧹 Legend state cleared for chart type switch: ${previousChartType} -> ${currentChartType}`
          )
        }

        // Log chart rendering state for debugging
        if (shouldRender) {
          console.log(`✅ Focused chart ${currentChartType} ready to render`)
        } else {
          console.log(`⏳ Focused chart ${currentChartType} waiting for data`)
        }

        previousChartType = currentChartType
      },
      { allowSignalWrites: true }
    )

    // Effect to handle data loading completion and trigger chart initialization
    effect(
      () => {
        const chartData = this.currentChartData()
        const isLoading = this.isCurrentChartLoading()
        const chartType = this.chartTitle()

        // If data just finished loading and we have valid data, initialize the chart
        if (
          !isLoading &&
          chartData &&
          Array.isArray(chartData) &&
          chartData.length > 0 &&
          !this.#isInitializing
        ) {
          console.log(`📊 Data loaded for ${chartType}, initializing chart`)
          // Small delay to ensure DOM is ready
          setTimeout(() => {
            this.initializeChartData(chartData)
          }, 50)
        }
      },
      { allowSignalWrites: true }
    )

    // Note: Reset logic is now handled at the source in NgRx store
    // when setEciShowDetails(true) is called, ensuring proper state management
  }

  public ngAfterViewInit(): void {
    // Initialize chart with current data when component loads
    const chartData = this.currentChartData()
    if (chartData && chartData.length > 0) {
      console.log('ngAfterViewInit: Initializing chart for', this.chartTitle())
      this.initializeChartData(chartData)
    }
  }

  private initializeChartData(documentTypes: any[]): void {
    try {
      // Set initialization flag to prevent infinite loops
      this.#isInitializing = true

      if (
        !documentTypes ||
        !Array.isArray(documentTypes) ||
        documentTypes.length === 0
      ) {
        console.warn(
          'Invalid or empty document types data provided to initializeChartData'
        )
        this.#initializeEmptyChart()
        this.#isInitializing = false
        return
      }

      const sortedDocuTypes = [...documentTypes].sort((a, b) => {
        const countA = a?.count || 0
        const countB = b?.count || 0
        return countB - countA
      })

      // Check if this is relevance data (no subcategories)
      const isRelevanceData = sortedDocuTypes.every(
        (item) => !item.subcategories || item.subcategories.length === 0
      )

      if (isRelevanceData) {
        this.#initializeRelevanceChart(sortedDocuTypes)
        // Clear initialization flag immediately for relevance charts
        this.#isInitializing = false
        return
      }

      const calculationResult = calculateSunburstData(sortedDocuTypes)
      if (!calculationResult) {
        console.warn('Failed to calculate sunburst data')
        this.#initializeEmptyChart()
        this.#isInitializing = false
        return
      }

      const {
        parentLabels,
        childrenLabels,
        chartOneTotal,
        chartOneSubTotal,
        chartOnePercents,
        chartOneChildPercents,
        allCountsOne,
        allValsOne,
        formattedCounts,
      } = calculationResult

      this.parentLabels = parentLabels
      this.childrenLabels = childrenLabels
      this.chartOneTotal = chartOneTotal
      this.chartOneSubTotal = chartOneSubTotal
      this.chartOnePercents = chartOnePercents
      this.chartOneChildPercents = chartOneChildPercents
      this.allCountsOne = allCountsOne
      this.allValsOne = allValsOne
      this.formattedCounts = formattedCounts

      this.labels = ['Total']
      this.parents = ['']

      const parentsLen = this.parentLabels.length
      this.labels.push(...this.parentLabels)
      this.parents.push(...Array.from({ length: parentsLen }, () => 'Total'))

      for (let i = 0; i < this.childrenLabels.length; i++) {
        this.labels.push(...this.childrenLabels[i])
        const len = this.childrenLabels[i].length
        this.parents.push(
          ...Array.from({ length: len }, () => this.parentLabels[i])
        )
      }

      // structure for the sunburst chart for angular - adjusting from React version
      const updatedVals = [...this.allValsOne]
      this.parents.forEach((parent, idx) => {
        if (parent === 'Total' || idx === 0) {
          updatedVals[idx] = 0
        }
      })

      // Store original values for smooth expansion/collapse
      this.originalValues = [...updatedVals]

      // Use default hole size during initialization - hole size will be updated separately when needed
      const holeSize = 0.5

      // Generate colors for the chart based on chart type
      const colors = this.chartColors()
      const chartColors = this.#generateChartColors(this.labels.length, colors)

      // Update chart data signal
      this.chartDataSignal.set([
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: updatedVals,
          hole: holeSize,
          textinfo: 'label',
          marker: {
            colors: chartColors,
            line: { width: 0.5, color: '#ffffff' }, // Reduced gap by 40% - smaller borders between segments
          },
          leaf: { opacity: 0.9 }, // Slightly transparent leaves for better separation
        },
      ])

      // Update chart layout signal
      this.chartLayoutSignal.set({
        autosize: true,
        automargin: false,
        branchvalues: 'remainder', // Required for donut
        margin: { t: 20, r: 20, b: 20, l: 20 },
        height: 400,
        width: undefined, // Let it auto-size width
        showlegend: false,
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
      })
      // Update chart config signal
      this.chartConfigSignal.set({
        responsive: true,
        displayModeBar: false,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toImage',
          'sendDataToCloud',
          'editInChartStudio',
          'zoom2d',
          'select2d',
          'pan2d',
          'lasso2d',
          'autoScale2d',
          'resetScale2d',
        ],
        // Ensure proper resizing behavior
        autosizable: true,
        fillFrame: false,
        frameMargins: 0,
        // Enable interactive cursor
        staticPlot: false,
        scrollZoom: false,
        doubleClick: false,
        showTips: false,
        editable: false,
      })

      // Store original colors for highlighting functionality
      this.originalColors = [...chartColors]

      // Sync legend colors with chart colors for root level elements
      this.#syncLegendColors(documentTypes, chartColors)

      // Initialize table data immediately for legend
      this.#initializeTableDataSafely(documentTypes)

      // Update hole size and finalize chart after a short delay
      setTimeout(() => {
        this.#updateChartHoleSize()
        this.#forceChartRedraw()
        // Clear initialization flag after chart is fully initialized
        this.#isInitializing = false
        console.log('Chart initialization completed for', this.chartTitle())
      }, 150)
    } catch (error) {
      console.error('Error initializing chart data:', error)
      this.#initializeEmptyChart()
      this.#isInitializing = false
    }
  }

  #generateChartColors(labelCount: number, colorScheme: string[]): string[] {
    const colors: string[] = []
    // Skip index 0 (transparent) and use colors starting from index 1
    const availableColors = colorScheme.slice(1)

    // Check if this is relevance chart for special handling
    const isRelevanceChart = this.chartTitle() === SunburstChartType.Relevance

    if (isRelevanceChart) {
      // For relevance charts, use sequential order as specified
      for (let i = 0; i < labelCount; i++) {
        if (i === 0) {
          colors.push('transparent')
        } else {
          const colorIndex = (i - 1) % availableColors.length
          colors.push(availableColors[colorIndex])
        }
      }
    } else {
      // For other charts, implement proper parent-child color relationship
      const parentColorMap = new Map<string, string>()
      const usedParentColors = new Set<string>()
      let parentColorIndex = 0

      // Build a more robust parent-child mapping
      // First pass: identify all parent elements and assign colors
      const parentIndices = new Set<number>()

      for (let i = 0; i < labelCount; i++) {
        if (i === 0) {
          // Root element
          colors.push('transparent')
          continue
        }

        const label = this.labels[i]
        const parent = this.parents[i]
        const isChildElement = label && label.match(/^\d+:\s/)

        if (!isChildElement && parent === 'Total') {
          // This is a parent element (inner ring)
          parentIndices.add(i)

          // Assign a unique color
          let baseColor =
            availableColors[parentColorIndex % availableColors.length]

          // Ensure no duplicate colors for parent elements
          let attempts = 0
          while (
            usedParentColors.has(baseColor) &&
            attempts < availableColors.length
          ) {
            parentColorIndex++
            baseColor =
              availableColors[parentColorIndex % availableColors.length]
            attempts++
          }

          colors.push(baseColor)
          parentColorMap.set(label, baseColor)
          usedParentColors.add(baseColor)
          parentColorIndex++
        } else {
          // Placeholder for child elements, will be filled in second pass
          colors.push('')
        }
      }

      // Second pass: assign child colors based on their parent's color
      for (let i = 1; i < labelCount; i++) {
        if (parentIndices.has(i)) {
          // Already processed in first pass
          continue
        }

        const label = this.labels[i]
        const parent = this.parents[i]
        const isChildElement = label && label.match(/^\d+:\s/)

        if (isChildElement) {
          // This is a child element (outer ring) - use lighter shade of parent's color
          const parentColor = parentColorMap.get(parent)

          if (parentColor) {
            const lighterColor = this.#getLighterShade(parentColor)
            colors[i] = lighterColor
          } else {
            // Fallback: use a default color
            colors[i] = availableColors[i % availableColors.length]
          }
        } else {
          // This might be a parent that wasn't caught in the first pass
          // Assign it a unique color
          let baseColor =
            availableColors[parentColorIndex % availableColors.length]

          let attempts = 0
          while (
            usedParentColors.has(baseColor) &&
            attempts < availableColors.length
          ) {
            parentColorIndex++
            baseColor =
              availableColors[parentColorIndex % availableColors.length]
            attempts++
          }

          colors[i] = baseColor
          parentColorMap.set(label, baseColor)
          usedParentColors.add(baseColor)
          parentColorIndex++
        }
      }
    }

    return colors
  }

  #getLighterShade(hexColor: string): string {
    // Convert hex to RGB
    const hex = hexColor.replace('#', '')
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)

    // Lighten by adding 30% towards white (255) for better contrast
    const lightenFactor = 0.3
    const newR = Math.round(r + (255 - r) * lightenFactor)
    const newG = Math.round(g + (255 - g) * lightenFactor)
    const newB = Math.round(b + (255 - b) * lightenFactor)

    // Convert back to hex
    const toHex = (n: number): string => {
      const hex = n.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`
  }

  /**
   * Generate colors for restructured chart using exact original parent-child relationship
   */
  #generateParentChildColors(selectedData: any): string[] {
    try {
      console.log('Generating parent-child colors for:', selectedData)

      // Check if this is relevance chart (no subcategories structure)
      const isRelevanceChart = this.chartTitle() === SunburstChartType.Relevance

      if (isRelevanceChart) {
        // Relevance chart doesn't have subcategories - return original colors
        console.log('Relevance chart detected - using original colors')
        return [...this.originalColors]
      }

      if (
        !selectedData.subcategories ||
        selectedData.subcategories.length === 0
      ) {
        console.log('No subcategories found - using original colors')
        return [...this.originalColors]
      }

      // Find the exact original colors for each subcategory
      const colors = ['transparent'] // Root is always transparent

      for (const subcategory of selectedData.subcategories) {
        const subcategoryName = subcategory.subcategory

        // Find the original color for this specific subcategory
        const originalColor = this.#findOriginalSubcategoryColor(
          selectedData.category,
          subcategoryName
        )

        if (originalColor && originalColor !== 'transparent') {
          colors.push(originalColor)
        } else {
          // Fallback: use parent color with lighter shade
          const parentColor = this.#findOriginalNodeColor(selectedData)
          if (parentColor && parentColor !== 'transparent') {
            const lighterColor = this.#getLighterShade(parentColor)
            colors.push(lighterColor)
          } else {
            // Final fallback: use sequential colors
            const fallbackColors = this.chartColors().slice(1)
            const colorIndex = (colors.length - 1) % fallbackColors.length
            colors.push(fallbackColors[colorIndex])
          }
        }
      }

      console.log('Generated exact parent-child colors:', {
        selectedCategory: selectedData.category,
        subcategories: selectedData.subcategories.map(
          (sub: any) => sub.subcategory
        ),
        colors: colors,
      })
      return colors
    } catch (error) {
      console.error('Error generating parent-child colors:', error)
      // Fallback to sequential colors
      return [
        'transparent',
        ...this.chartColors().slice(1, selectedData.subcategories.length + 1),
      ]
    }
  }

  /**
   * Create a color variation with specified lightening factor
   */
  #createColorVariation(hexColor: string, lightenFactor: number): string {
    // Convert hex to RGB
    const hex = hexColor.replace('#', '')
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)

    // Apply lightening factor
    const newR = Math.round(r + (255 - r) * lightenFactor)
    const newG = Math.round(g + (255 - g) * lightenFactor)
    const newB = Math.round(b + (255 - b) * lightenFactor)

    // Convert back to hex
    const toHex = (n: number): string => {
      const hex = n.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`
  }

  #findOriginalNodeColor(selectedNode: any): string | null {
    try {
      // Find the color of the selected node from the original chart
      if (
        !selectedNode ||
        !this.originalColors ||
        this.originalColors.length === 0
      ) {
        return null
      }

      const nodeName = selectedNode.category || selectedNode.name
      if (!nodeName) {
        return null
      }

      // Find the index of this node in the current labels
      const nodeIndex = this.labels.findIndex((label) => label === nodeName)

      if (nodeIndex !== -1 && nodeIndex < this.originalColors.length) {
        const originalColor = this.originalColors[nodeIndex]
        console.log(`Found original color for ${nodeName}:`, originalColor)
        return originalColor
      }

      console.warn(`Could not find original color for node: ${nodeName}`)
      return null
    } catch (error) {
      console.error('Error finding original node color:', error)
      return null
    }
  }

  /**
   * Find the exact original color for a specific subcategory
   */
  #findOriginalSubcategoryColor(
    parentCategory: string,
    subcategoryName: string
  ): string | null {
    try {
      if (
        !this.originalColors ||
        this.originalColors.length === 0 ||
        !this.labels
      ) {
        return null
      }

      // Look for the subcategory in the original labels
      // Subcategories are typically formatted as "number: subcategory name"
      const subcategoryPattern = new RegExp(
        `^\\d+:\\s*${subcategoryName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`
      )

      const subcategoryIndex = this.labels.findIndex(
        (label) => subcategoryPattern.test(label) || label === subcategoryName
      )

      if (
        subcategoryIndex !== -1 &&
        subcategoryIndex < this.originalColors.length
      ) {
        const originalColor = this.originalColors[subcategoryIndex]
        console.log(
          `Found original subcategory color for ${subcategoryName}:`,
          originalColor
        )
        return originalColor
      }

      console.warn(
        `Could not find original color for subcategory: ${subcategoryName}`
      )
      return null
    } catch (error) {
      console.error('Error finding original subcategory color:', error)
      return null
    }
  }

  #initializeEmptyChart(): void {
    this.labels = ['No Data']
    this.parents = ['']

    // Update chart data signal
    this.chartDataSignal.set([
      {
        type: 'sunburst',
        labels: this.labels,
        parents: this.parents,
        values: [1],
        hole: 0.5,
        textinfo: 'label',
        marker: {
          colors: ['#e5e7eb'],
          line: { width: 0.5, color: '#ffffff' },
        },
        leaf: { opacity: 0.9 },
      },
    ])

    // Update chart layout signal
    this.chartLayoutSignal.set({
      autosize: true,
      automargin: false,
      branchvalues: 'remainder',
      margin: { t: 20, r: 20, b: 20, l: 20 },
      height: 400,
      width: undefined,
      showlegend: false,
      paper_bgcolor: 'rgba(0,0,0,0)',
      plot_bgcolor: 'rgba(0,0,0,0)',
    })
    // Update chart config signal
    this.chartConfigSignal.set({
      responsive: true,
      displayModeBar: false,
      displaylogo: false,
      autosizable: true,
      fillFrame: false,
      frameMargins: 0,
      // Enable interactive cursor
      staticPlot: false,
      scrollZoom: false,
      doubleClick: false,
      showTips: false,
      editable: false,
    })
  }

  #forceChartRedraw(): void {
    try {
      if (
        this.isBrowser &&
        typeof window !== 'undefined' &&
        (window as any).Plotly
      ) {
        const plotlyElement = document.querySelector(
          'plotly-plot div[id^="plotly-div-"]'
        )
        if (plotlyElement) {
          console.log('Forcing chart redraw and resize')
          // Force a resize first to ensure proper dimensions
          ;(window as any).Plotly.Plots.resize(plotlyElement)
          // Then redraw to ensure content is properly rendered
          ;(window as any).Plotly.redraw(plotlyElement)
          // Additional resize after redraw to handle any layout changes
          setTimeout(() => {
            ;(window as any).Plotly.Plots.resize(plotlyElement)
          }, 50)
        } else {
          console.warn('Plotly element not found for redraw')
        }
      }
    } catch (error) {
      console.error('Error forcing chart redraw:', error)
    }
  }

  #highlightSelectedSegment(selectedLabel: string): void {
    try {
      console.log('Highlighting selected segment:', selectedLabel)
      const graph = this.graph()
      if (!graph || !graph.data || !graph.data[0]) {
        console.warn('Chart data not available for highlighting')
        return
      }

      const chartData = graph.data[0]

      // Get current chart labels
      const currentLabels = chartData.labels || this.labels

      // Always get fresh base colors to avoid corruption from previous highlighting
      let baseColors: string[]
      if (this.isLegendModifiedSignal()) {
        // Chart is restructured - regenerate the original restructured colors
        const legendData = this.legendSelectedDataSignal()
        if (legendData) {
          baseColors = this.#generateParentChildColors(legendData)
        } else {
          baseColors = this.originalColors
        }
      } else {
        // Chart is in original state - use stored original colors
        baseColors = this.originalColors
      }

      if (!baseColors || baseColors.length === 0) {
        console.warn('No base colors available for highlighting')
        return
      }

      // Create a fresh copy of base colors to modify
      const colors = [...baseColors]

      // Find the index of the selected label in current chart
      const selectedIndex = currentLabels.findIndex(
        (label: string) => label === selectedLabel
      )

      if (selectedIndex === -1) {
        console.warn(
          'Selected label not found in current chart labels:',
          selectedLabel,
          'Available labels:',
          currentLabels
        )
        return
      }

      console.log(
        `Found selected label "${selectedLabel}" at index ${selectedIndex}`
      )

      // Apply highlighting: gray out all segments except selected one
      for (let i = 0; i < colors.length; i++) {
        if (i === 0) {
          // Keep root transparent
          continue
        } else if (i === selectedIndex) {
          // Keep selected segment at original color (no change)
          console.log(
            `Keeping selected segment at index ${i} with original color: ${colors[i]}`
          )
          continue
        } else {
          // Gray out other segments with 40% opacity using base color
          const baseColor = colors[i]
          if (baseColor && baseColor !== 'transparent') {
            // Only apply gray-out if the color is in hex format (not already grayed out)
            if (baseColor.startsWith('#')) {
              colors[i] = this.#applyGrayOpacity(baseColor, 0.4)
              console.log(
                `Graying out segment at index ${i}: ${baseColor} -> ${colors[i]}`
              )
            } else {
              // Color is already processed, keep it as is
              console.log(
                `Segment at index ${i} already processed: ${baseColor}`
              )
            }
          }
        }
      }

      // Update chart colors using Plotly.restyle for smooth highlighting
      if (
        this.isBrowser &&
        typeof window !== 'undefined' &&
        (window as any).Plotly
      ) {
        const plotlyElement = document.querySelector(
          'plotly-plot div[id^="plotly-div-"]'
        )
        if (plotlyElement) {
          // Use Plotly.restyle for smooth color transition
          ;(window as any).Plotly.restyle(
            plotlyElement,
            {
              'marker.colors': [colors],
            },
            [0],
            {
              transition: {
                duration: 150,
                easing: 'cubic-in-out',
              },
            }
          )
        } else {
          // Fallback to direct update
          chartData.marker.colors = colors
          this.#forceChartRedraw()
        }
      } else {
        // Fallback when Plotly is not available
        chartData.marker.colors = colors
        this.#forceChartRedraw()
      }

      console.log('Segment highlighting applied successfully')
    } catch (error) {
      console.error('Error highlighting selected segment:', error)
    }
  }

  #applyGrayOpacity(hexColor: string, opacity: number): string {
    try {
      // Convert hex to RGB
      const hex = hexColor.replace('#', '')
      const r = parseInt(hex.substring(0, 2), 16)
      const g = parseInt(hex.substring(2, 4), 16)
      const b = parseInt(hex.substring(4, 6), 16)

      // Convert to grayscale and apply opacity
      const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b)

      return `rgba(${gray}, ${gray}, ${gray}, ${opacity})`
    } catch (error) {
      console.error('Error applying gray opacity:', error)
      return `rgba(128, 128, 128, ${opacity})` // Fallback gray
    }
  }

  public onChartClick(event: any): void {
    try {
      if (
        !event ||
        !event.points ||
        !Array.isArray(event.points) ||
        event.points.length === 0
      ) {
        console.warn('Invalid chart click event:', event)
        return
      }

      const clickedPoint = event.points[0]

      if (!clickedPoint || !clickedPoint.label) {
        console.warn('Invalid clicked point:', clickedPoint)
        return
      }

      // Get the appropriate data based on chart type
      const chartData = this.currentChartData()

      if (!chartData || !Array.isArray(chartData)) {
        console.warn('No chart data available for interaction')
        return
      }

      // Use focused mode click handling logic
      this.#handleFocusedModeClick(clickedPoint, chartData)
    } catch (error) {
      console.error('Error handling chart click:', error)
    }
  }

  public onLegendClick(event: { legendName: string; selectedItem: any }): void {
    try {
      const { legendName, selectedItem } = event
      console.log(`Legend clicked in sunburst: ${legendName}`, selectedItem)
      console.log('chartIsExpanded:', this.chartIsExpanded())

      // Get the appropriate data based on chart type
      const chartData = this.currentChartData()

      if (!chartData || !Array.isArray(chartData)) {
        console.warn('No chart data available for legend interaction')
        return
      }

      // Use focused mode legend click handling logic
      this.#handleFocusedModeLegendClick(legendName, chartData)
    } catch (error) {
      console.error('Error handling legend click:', error)
    }
  }

  #handleFocusedModeClick(clickedPoint: any, chartData: any[]): void {
    try {
      const chartType = this.chartTitle()
      const currentState = this.chartDrillDownState()
      const isCurrentlyExpanded = currentState?.isExpanded || false

      console.log(`${chartType} - Chart clicked:`, clickedPoint, {
        currentState,
        isExpanded: isCurrentlyExpanded,
      })

      // Check if this is relevance data (single layer)
      const isRelevanceData = chartData.every(
        (item) => !item.subcategories || item.subcategories.length === 0
      )

      if (isRelevanceData && clickedPoint.parent === 'Total') {
        // For relevance data, clicking a segment should behave like legend click
        console.log(`${chartType} - Relevance segment clicked:`, clickedPoint)

        // Store selected node data for relevance selection
        const relevanceData = chartData.find(
          (item: any) =>
            item?.category === clickedPoint.label ||
            item?.name === clickedPoint.label
        )
        if (relevanceData) {
          this.aiFacade.setChartSelectedNode(this.chartTitle(), relevanceData)
        }

        // Call the existing legend click logic to ensure consistent behavior
        this.onLegendClick({
          legendName: clickedPoint.label,
          selectedItem: clickedPoint,
        })
        return
      }

      // Simple expand/collapse logic (same as original)

      if (!isCurrentlyExpanded && clickedPoint.parent === 'Total') {
        // Inner ring node clicked - expand to show children AND show file details
        const childLabel = clickedPoint.label
        const childData = chartData.find(
          (item: any) =>
            item?.category === childLabel || item?.name === childLabel
        )

        if (!childData) {
          console.warn('Child data not found for label:', childLabel)
          return
        }

        console.log(
          `${chartType} - Inner ring clicked, expanding chart for:`,
          childLabel
        )

        // Store selected node data for inner ring selection (no highlighting per requirements)
        this.aiFacade.setChartSelectedNode(this.chartTitle(), childData)

        const formatted = formatChildData(childData)
        if (formatted && Array.isArray(formatted)) {
          this.aiFacade.drillDownToNextLevel(chartType, childData, formatted)
          this.aiFacade.updateChartTableData(chartType, formatted)
        }
      } else if (isCurrentlyExpanded && clickedPoint.parent === 'Total') {
        // Parent segment clicked while expanded - collapse back to root
        console.log(`${chartType} - Collapsing back to root view`)

        // Store selected node data for parent segment selection
        const parentData = chartData.find(
          (item: any) =>
            item?.category === clickedPoint.label ||
            item?.name === clickedPoint.label
        )
        if (parentData) {
          this.aiFacade.setChartSelectedNode(this.chartTitle(), parentData)
        }

        this.aiFacade.drillBackToPreviousLevel(chartType)

        const formatted = formatParentData(chartData)
        if (formatted && Array.isArray(formatted)) {
          this.aiFacade.updateChartTableData(chartType, formatted)
        }
      } else if (isCurrentlyExpanded) {
        // Outer ring node clicked - should behave like legend click
        console.log(
          `${chartType} - Outer ring node selected:`,
          clickedPoint.label
        )

        // Call the existing legend click logic to ensure consistent behavior
        this.onLegendClick({
          legendName: clickedPoint.label,
          selectedItem: clickedPoint,
        })
      }

      // Update hole size after state change
      setTimeout(() => {
        this.#updateChartHoleSize()
      }, 50)
    } catch (error) {
      console.error('Error handling focused mode click:', error)
    }
  }

  #handleFocusedModeLegendClick(legendName: string, chartData: any[]): void {
    try {
      const chartType = this.chartTitle()
      console.log(`${chartType} - Legend clicked: ${legendName}`)

      // Ensure we have valid chart data for the current chart type
      if (!chartData || !Array.isArray(chartData) || chartData.length === 0) {
        console.warn(
          `${chartType} - No valid chart data for legend interaction`
        )
        return
      }

      // For relevance charts, only do gray-out highlighting (no restructuring)
      const isRelevanceChart = chartType === SunburstChartType.Relevance

      if (isRelevanceChart) {
        console.log(
          `${chartType} - Relevance chart: applying gray-out only for: ${legendName}`
        )
        this.#grayOutOtherNodes(legendName)

        // Set legend modified state for relevance charts to show back button
        const clickedData = chartData.find(
          (item) => item.category === legendName || item.name === legendName
        )
        this.#setLegendModifiedState(true, clickedData)

        // Store selected node data in state for file list component
        this.aiFacade.setChartSelectedNode(this.chartTitle(), clickedData)
        return
      }

      // Check if chart is already restructured by previous legend click
      const isLegendModified = this.isLegendModifiedSignal()
      console.log(`${chartType} - Legend modified state: ${isLegendModified}`)

      if (isLegendModified) {
        // Chart is already restructured - only gray out and highlight
        console.log(
          `${chartType} - Chart already restructured, applying gray-out only for: ${legendName}`
        )
        this.#grayOutOtherNodes(legendName)

        // Store selected node data for restructured chart
        const legendData = this.legendSelectedDataSignal()
        if (legendData && legendData.subcategories) {
          const clickedSubcategory = legendData.subcategories.find(
            (sub: any) => sub.subcategory === legendName
          )
          if (clickedSubcategory) {
            this.aiFacade.setChartSelectedNode(
              this.chartTitle(),
              clickedSubcategory
            )
          }
        }
        return
      }

      // Chart is in original state - find the data for the clicked legend item
      const clickedData = chartData.find(
        (item: any) =>
          item?.category === legendName || item?.name === legendName
      )

      if (!clickedData) {
        console.warn('Legend data not found for:', legendName)
        return
      }

      // Check if the clicked legend item has children
      const hasChildren =
        clickedData.subcategories && clickedData.subcategories.length > 0

      if (hasChildren) {
        // Restructure chart to show only the children of the selected item as a single ring
        console.log(
          `${chartType} - Restructuring chart for legend: ${legendName}`
        )
        this.#restructureChartForLegend(clickedData, chartType)
      } else {
        // Gray out other nodes, highlight only the selected item
        console.log(
          `${chartType} - Graying out other nodes for legend: ${legendName}`
        )
        this.#grayOutOtherNodes(legendName)
      }

      // Set a flag to indicate chart has been modified by legend click
      this.#setLegendModifiedState(true, clickedData)

      // Store selected node data in state for file list component
      this.aiFacade.setChartSelectedNode(this.chartTitle(), clickedData)
    } catch (error) {
      console.error('Error handling focused mode legend click:', error)
    }
  }

  public onChartRelayout(event: any): void {
    // Handle chart relayout events to ensure proper responsive behavior
    try {
      console.log('Chart relayout event:', event)
      // Force a resize if needed
      setTimeout(() => {
        if (
          this.isBrowser &&
          typeof window !== 'undefined' &&
          (window as any).Plotly
        ) {
          const plotlyElement = document.querySelector(
            'plotly-plot div[id^="plotly-div-"]'
          )
          if (plotlyElement) {
            ;(window as any).Plotly.Plots.resize(plotlyElement)
          }
        }
      }, 100)
    } catch (error) {
      console.error('Error handling chart relayout:', error)
    }
  }

  public onChartRestyle(event: any): void {
    // Handle chart restyle events
    try {
      console.log('Chart restyle event:', event)
    } catch (error) {
      console.error('Error handling chart restyle:', error)
    }
  }

  #updateChartHoleSize(): void {
    try {
      // Calculate hole size based on actual chart state, not just expanded flag
      const holeSize = this.#calculateOptimalHoleSize()
      const graph = this.graph()

      if (graph && graph.data && graph.data[0]) {
        // Use smooth animation for hole size changes
        if (
          this.isBrowser &&
          typeof window !== 'undefined' &&
          (window as any).Plotly
        ) {
          const plotlyElement = document.querySelector(
            'plotly-plot div[id^="plotly-div-"]'
          )
          if (plotlyElement) {
            // Animate the hole size change smoothly
            ;(window as any).Plotly.restyle(
              plotlyElement,
              {
                hole: holeSize,
              },
              [0],
              {
                transition: {
                  duration: 300,
                  easing: 'cubic-in-out',
                },
              }
            )
          }
        } else {
          // Fallback for when Plotly is not available
          graph.data[0].hole = holeSize
          this.#forceChartRedraw()
        }
      }
    } catch (error) {
      console.error('Error updating chart hole size:', error)
    }
  }

  #calculateOptimalHoleSize(): number {
    try {
      const currentState = this.chartDrillDownState()

      // Simple toggle: expanded = smaller hole, collapsed = larger hole
      if (currentState?.isExpanded) {
        // Expanded state (showing children) - smaller hole
        return 0.4
      }
      // Collapsed state (showing parents) - larger hole
      return 0.5
    } catch (error) {
      console.error('Error calculating optimal hole size:', error)
      return 0.5 // Default fallback
    }
  }

  public goBack(): void {
    const chartType = this.chartTitle()

    // Handle drill-down back navigation
    if (this.chartCanGoBack()) {
      this.aiFacade.drillBackToPreviousLevel(chartType)

      // Update table data
      const chartData = this.currentChartData()
      const formatted = formatParentData(chartData)
      if (formatted && Array.isArray(formatted)) {
        this.aiFacade.updateChartTableData(chartType, formatted)
      }

      // Update hole size after state change
      setTimeout(() => {
        this.#updateChartHoleSize()
      }, 50)
    }
    // Handle legend modification reset (for relevance charts and other legend-only modifications)
    else if (this.isLegendModified()) {
      console.log('Resetting legend modifications for', chartType)
      this.resetToInitialState()
    }
  }

  #initializeTableDataSafely(chartData: any[]): void {
    try {
      const chartType = this.chartTitle()
      if (!chartType || !chartData || chartData.length === 0) {
        console.warn(
          'Cannot initialize table data - missing chart type or data'
        )
        return
      }

      // Always initialize table data for legend - this is safe during initialization
      const formatted = formatParentData(chartData)
      if (formatted && Array.isArray(formatted)) {
        console.log(
          `Initializing table data for ${chartType} with root level data:`,
          formatted
        )
        this.aiFacade.updateChartTableData(chartType, formatted)
      }
    } catch (error) {
      console.error('Error safely initializing table data for chart:', error)
    }
  }

  #transformEcaDocumentTypes(ecaDocumentTypes: any[]): any[] {
    try {
      if (!ecaDocumentTypes || !Array.isArray(ecaDocumentTypes)) {
        console.warn('Invalid ECA document types data for transformation')
        return []
      }

      return ecaDocumentTypes
        .map((docType) => {
          if (!docType) return null

          return {
            id: docType.docTypeId || 0,
            category: docType.docTypeName || 'Unknown',
            name: docType.docTypeName || 'Unknown',
            count: docType.docCount || 0,
            percentage: docType.percentage || 0,
            subcategories: docType.children
              ? docType.children.map((child: any) => ({
                  subcategory: child?.docTypeName || 'Unknown',
                  count: child?.docCount || 0,
                  percentage: child?.percentage || 0,
                }))
              : [],
          }
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA document types:', error)
      return []
    }
  }

  #transformEcaTopicsToDocumentTypes(ecaTopics: any[]): any[] {
    try {
      if (!ecaTopics || !Array.isArray(ecaTopics)) {
        console.warn('Invalid ECA topics data for transformation')
        return []
      }

      return ecaTopics
        .map((topic) => {
          if (!topic) return null

          return {
            id: topic.topicId || 0,
            category: topic.topicName || 'Unknown',
            name: topic.topicName || 'Unknown',
            count: topic.docCount || 0,
            percentage: topic.percentage || 0,
            subcategories: topic.children
              ? topic.children.map((child: any) => ({
                  subcategory: child?.topicName || 'Unknown',
                  count: child?.docCount || 0,
                  percentage: child?.percentage || 0,
                }))
              : [],
          }
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA topics:', error)
      return []
    }
  }

  #transformEcaRelevanceData(ecaRelevanceData: any[]): any[] {
    try {
      if (!ecaRelevanceData || !Array.isArray(ecaRelevanceData)) {
        console.warn('Invalid ECA relevance data for transformation')
        return []
      }

      // Calculate total for percentage calculation
      const total = ecaRelevanceData.reduce(
        (sum, item) => sum + (item?.docCount || 0),
        0
      )

      return ecaRelevanceData
        .map((relevanceItem, index) => {
          if (!relevanceItem) return null

          const count = relevanceItem.docCount || 0
          const percentage = total > 0 ? (count / total) * 100 : 0

          return {
            id: index + 1, // Use index + 1 as ID like other charts
            category: relevanceItem.relevanceType || 'Unknown',
            name: relevanceItem.relevanceType || 'Unknown',
            count: count,
            percentage: percentage,
            // For relevance, no subcategories - it's a single layer structure
            subcategories: [],
          }
        })
        .filter((item) => item !== null && item.count > 0)
    } catch (error) {
      console.error('Error transforming ECA relevance data:', error)
      return []
    }
  }

  #initializeRelevanceChart(relevanceData: any[]): void {
    try {
      console.log('Initializing relevance chart with data:', relevanceData)

      // For relevance data, create a simple single-layer structure
      const childValues = relevanceData.map((item) => item.count)
      const totalValue = childValues.reduce((sum, count) => sum + count, 0)

      const labels = ['Total', ...relevanceData.map((item) => item.category)]
      const parents = ['', ...relevanceData.map(() => 'Total')]
      const values = [totalValue, ...childValues]

      this.labels = labels
      this.parents = parents

      // Store original values for smooth expansion/collapse
      this.originalValues = [...values]

      // Generate colors for relevance chart (single layer)
      const chartColors = this.#generateColorsForRelevanceChart(
        relevanceData.length
      )

      // Store original colors for highlighting functionality
      this.originalColors = [...chartColors]

      // Set up legend color mapping for relevance chart
      this.legendColorMap.clear()
      relevanceData.forEach((item, index) => {
        // Index + 1 because index 0 is 'Total' which is transparent
        this.legendColorMap.set(item.category, chartColors[index + 1])
      })

      const holeSize = 0.5

      // Update chart data signal
      this.chartDataSignal.set([
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: values,
          hole: holeSize,
          textinfo: 'label',
          marker: {
            colors: chartColors,
            line: { width: 0.5, color: '#ffffff' },
          },
          leaf: { opacity: 0.9 },
          branchvalues: 'total',
          maxdepth: 2, // Only show root and first level for relevance
        },
      ])

      // Update chart layout signal
      this.chartLayoutSignal.set({
        autosize: true,
        automargin: false,
        margin: { t: 20, r: 20, b: 20, l: 20 },
        height: 400,
        width: undefined,
        showlegend: false,
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
      })

      // Update chart config signal
      this.chartConfigSignal.set({
        responsive: true,
        displayModeBar: false,
        displaylogo: false,
        autosizable: true,
        fillFrame: false,
        frameMargins: 0,
        staticPlot: false,
        scrollZoom: false,
        doubleClick: false,
        showTips: false,
        editable: false,
      })

      // Initialize table data immediately for legend
      this.#initializeTableDataSafely(relevanceData)

      // Force immediate chart rendering for relevance charts
      // Use multiple timeouts to ensure proper rendering
      setTimeout(() => {
        this.#forceChartRedraw()
      }, 10)

      setTimeout(() => {
        this.#forceChartRedraw()
        console.log('✅ Relevance chart initialization completed')
      }, 100)
    } catch (error) {
      console.error('Error initializing relevance chart:', error)
      this.#initializeEmptyChart()
      this.#isInitializing = false
    }
  }

  /**
   * Generate colors specifically for relevance chart (single layer)
   */
  #generateColorsForRelevanceChart(dataCount: number): string[] {
    const colors: string[] = []
    const relevanceColors = this.chartColors()

    // First color is transparent for 'Total'
    colors.push('transparent')

    // Add colors for each relevance item
    for (let i = 0; i < dataCount; i++) {
      // Skip index 0 (transparent) and use colors starting from index 1
      const colorIndex = (i % (relevanceColors.length - 1)) + 1
      colors.push(relevanceColors[colorIndex])
    }

    return colors
  }

  /**
   * Restructure chart to show only children of selected legend item as a single ring
   */
  #restructureChartForLegend(
    selectedData: any,
    chartType: SunburstChartType
  ): void {
    try {
      console.log('Restructuring chart for legend:', selectedData)

      if (
        !selectedData.subcategories ||
        selectedData.subcategories.length === 0
      ) {
        console.warn('No subcategories to restructure for:', selectedData)
        return
      }

      // Calculate total for percentages
      const total = selectedData.subcategories.reduce(
        (sum: number, sub: any) => sum + sub.count,
        0
      )

      // Update table data to show the restructured data
      const tableData = selectedData.subcategories.map((sub: any) => ({
        label: sub.subcategory,
        count: sub.count,
        percent: ((sub.count / total) * 100).toFixed(2),
      }))
      this.aiFacade.updateChartTableData(chartType, tableData)

      // Create Plotly data for single ring (like Relevance chart)
      const labels = [
        'Total',
        ...selectedData.subcategories.map((sub: any) => sub.subcategory),
      ]
      const parents = ['', ...selectedData.subcategories.map(() => 'Total')]
      const values = [
        total,
        ...selectedData.subcategories.map((sub: any) => sub.count),
      ]

      // Generate colors using parent-child relationship
      const colors = this.#generateParentChildColors(selectedData)

      // Create new chart data structure
      const newData = {
        labels,
        parents,
        values,
        type: 'sunburst',
        marker: {
          colors,
        },
        hovertemplate:
          '<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percentParent}<extra></extra>',
        maxdepth: 2, // Only show one level (Total + children)
        branchvalues: 'total',
        hole: 0.4, // Narrow hole for restructured view
      }

      const graph = this.graph()

      // Update chart layout for narrow hole
      const newLayout = {
        ...graph.layout,
        sunburstcolorway: colors,
        font: { size: 12 },
        margin: { t: 0, l: 0, r: 0, b: 0 },
        paper_bgcolor: 'transparent',
        plot_bgcolor: 'transparent',
      }

      // Update the chart signals directly - this will trigger Angular's reactive system
      this.chartDataSignal.set([newData])
      this.chartLayoutSignal.set(newLayout)

      // Set legend modified state to show back button
      this.#setLegendModifiedState(true)
    } catch (error) {
      console.error('Error restructuring chart for legend:', error)
    }
  }

  /**
   * Gray out other nodes, highlight only the selected item
   */
  #grayOutOtherNodes(selectedLabel: string): void {
    try {
      console.log('Graying out other nodes except:', selectedLabel)

      // For relevance charts, don't reset highlighting - just apply directly
      const isRelevanceChart = this.chartTitle() === SunburstChartType.Relevance

      if (isRelevanceChart) {
        // For relevance charts, directly highlight without resetting
        this.#highlightSelectedSegment(selectedLabel)
      } else {
        // For other charts, reset first then apply highlighting
        this.#resetHighlighting()
        this.#highlightSelectedSegment(selectedLabel)
      }
    } catch (error) {
      console.error('Error graying out other nodes:', error)
    }
  }

  /**
   * Reset highlighting to original colors
   */
  #resetHighlighting(): void {
    try {
      const graph = this.graph()
      if (!graph || !graph.data || !graph.data[0]) {
        return
      }

      const chartData = graph.data[0]

      // Get the base colors for the current chart state
      let baseColors: string[]
      if (this.isLegendModifiedSignal()) {
        // Chart is restructured - we need to get the original restructured colors
        // These should be stored when the chart was restructured
        const legendData = this.legendSelectedDataSignal()
        if (legendData) {
          baseColors = this.#generateParentChildColors(legendData)
        } else {
          baseColors = this.originalColors
        }
      } else {
        // Chart is in original state
        baseColors = this.originalColors
      }

      if (baseColors && baseColors.length > 0) {
        // Reset to base colors
        chartData.marker.colors = [...baseColors]
        console.log('Reset highlighting to base colors')
      }
    } catch (error) {
      console.error('Error resetting highlighting:', error)
    }
  }

  /**
   * Sync legend colors with chart colors for root level elements
   */
  #syncLegendColors(documentTypes: any[], chartColors: string[]): void {
    try {
      if (!documentTypes || !chartColors) {
        return
      }

      // Create a mapping of legend items to their corresponding chart colors
      const legendColorMap = new Map<string, string>()

      // For root level elements, map each category to its chart color
      documentTypes.forEach((item, index) => {
        const categoryName = item.category || item.name
        if (categoryName && chartColors[index + 1]) {
          // Skip index 0 (transparent) and map to actual chart colors
          legendColorMap.set(categoryName, chartColors[index + 1])
        }
      })

      // Store the legend color mapping for use by the data table component
      this.legendColorMap = legendColorMap
      console.log('Synced legend colors with chart colors:', legendColorMap)
    } catch (error) {
      console.error('Error syncing legend colors:', error)
    }
  }

  /**
   * Set the legend modified state
   */
  #setLegendModifiedState(isModified: boolean, selectedData?: any): void {
    this.isLegendModifiedSignal.set(isModified)
    this.legendSelectedDataSignal.set(selectedData || null)
  }

  /**
   * Check if chart has been modified by legend clicks
   */
  public isLegendModified(): boolean {
    return this.isLegendModifiedSignal()
  }

  /**
   * Determine if back button should be shown
   */
  public shouldShowBackButton(): boolean {
    return this.isLegendModified() || this.chartCanGoBack()
  }

  /**
   * Get the correct legend color for a given category name
   */
  public getLegendColor(categoryName: string, fallbackIndex: number): string {
    // First try to get the color from the synchronized mapping
    const mappedColor = this.legendColorMap.get(categoryName)
    if (mappedColor) {
      return mappedColor
    }

    // Fallback to sequential color scheme
    const colors = this.chartColors()
    return colors[fallbackIndex + 1] || colors[1] || '#1f77b4'
  }

  /**
   * Reset chart to initial state (called by back button and view details)
   */
  public resetToInitialState(): void {
    try {
      console.log(
        '🔄 RESET TO INITIAL STATE: Resetting everything like back button'
      )

      // Prevent infinite loops during reset
      if (this.#isInitializing) {
        console.log(
          'Already initializing, skipping reset to prevent infinite loop'
        )
        return
      }

      // Set initialization flag to prevent infinite loops during reset
      this.#isInitializing = true

      // 1. Reset legend modification state FIRST
      this.#setLegendModifiedState(false, null)

      // 2. Reset drill-down state in store for ALL chart types
      const chartType = this.chartTitle()
      this.aiFacade.resetChartDrillDown(chartType)
      const graph = this.graph()

      // 3. Clear any highlighting or color modifications for ALL chart types
      if (graph && graph.data && graph.data[0]) {
        graph.data[0].marker.colors = [...this.originalColors]
        this.chartDataSignal.set(graph.data)
        this.chartLayoutSignal.set(graph.layout)
      }

      // 4. Reset any chart-specific state
      this.lastChartLevel = 0
      this.lastChartExpanded = false

      // 5. Clear the current graph to force complete re-render
      this.chartDataSignal.set([])
      this.chartLayoutSignal.set({})

      // 6. Wait for data to be ready before reinitializing
      const chartData = this.currentChartData()
      const isLoading = this.isCurrentChartLoading()

      if (!isLoading && chartData && chartData.length > 0) {
        // Data is ready, initialize immediately
        setTimeout(() => {
          this.initializeChartData(chartData)

          // Update table data to show parent data
          const formatted = formatParentData(chartData)
          if (formatted && Array.isArray(formatted)) {
            this.aiFacade.updateChartTableData(chartType, formatted)
          }

          this.#isInitializing = false
          console.log('✅ RESET COMPLETE for', chartType)
        }, 100)
      } else {
        // Data is still loading, the effect will handle initialization when ready
        this.#isInitializing = false
        console.log(
          '⏳ Reset initiated, waiting for data to load for',
          chartType
        )
      }
    } catch (error) {
      console.error('Error resetting to initial state:', error)
      this.#isInitializing = false
    }
  }
}
