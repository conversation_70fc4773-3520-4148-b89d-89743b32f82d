import { Component, inject, Input } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons'
import {
  AiFacade,
  ECADashboardRequestModel,
  ECADashboardType,
} from '@venio/data-access/ai'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { combineLatest, filter, take } from 'rxjs'

@Component({
  selector: 'venio-title-and-download',
  standalone: true,
  imports: [KENDO_BUTTONS, SvgLoaderDirective],
  templateUrl: './title-and-download.component.html',
  styleUrl: './title-and-download.component.scss',
})
export class TitleAndDownloadComponent {
  @Input() public title = ''

  @Input() public dashboardType: ECADashboardType

  private searchFacade = inject(SearchFacade)

  private documentFacade = inject(DocumentsFacade)

  private aiFacade = inject(AiFacade)

  private activatedRoute = inject(ActivatedRoute)

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public readonly ECADashboardType = ECADashboardType

  public readonly svgOpenNew: SVGIcon = hyperlinkOpenSmIcon

  public openFocusedSection(): void {
    this.aiFacade.setEciFocusedSectionOpened(true)
  }

  public downloadCSV(): void {
    combineLatest([
      this.searchFacade.getSearchResponse$,
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getUnselectedDocuments$,
      this.documentFacade.getIsBatchSelected$,
    ])
      .pipe(
        filter(([response]) => Boolean(response)),
        take(1)
      )
      .subscribe(
        ([
          searchResponse,
          selectedFileIds,
          unSelectedFileIds,
          isBatchSelected,
        ]) => {
          const {
            tempTables: { searchResultTempTable },
          } = searchResponse

          const requestModel: ECADashboardRequestModel = {
            searchTempTable: searchResultTempTable,
            selectedFileIds: selectedFileIds,
            unSelectedFileIds: unSelectedFileIds,
            isBatchSelection: isBatchSelected,
            dashboardType: this.dashboardType,
          }
          this.aiFacade.downloadCSV(this.projectId, requestModel)
        }
      )
  }
}
