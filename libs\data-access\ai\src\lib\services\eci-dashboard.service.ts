import { Injectable, inject } from '@angular/core'
import { Observable, of } from 'rxjs'
import {
  CustodianModel,
  DocumentTypeModel,
  FileDataModel,
  WordCloudData,
  RelevanceChartData,
  BarChartData,
  EciDashboardFilters,
  ECADashboardRequestModel,
} from '../models/interfaces/eci-dashboard.model'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { HttpClient } from '@angular/common/http'
import { environment } from '@venio/shared/environments'

@Injectable({
  providedIn: 'root',
})
export class EciDashboardService {
  private readonly httpClient = inject(HttpClient)

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  /**
   * Load ECI Dashboard data (Legacy method - kept for backward compatibility)
   * @param projectId - The project ID to fetch data for
   * @param jobId - Optional job ID for specific job data
   * @param filters - Optional filters to apply to the data
   * @deprecated Use individual ECA API methods instead
   */
  public loadDashboardData(
    projectId: number,
    jobId?: number,
    filters?: EciDashboardFilters
  ): Observable<{
    custodians: CustodianModel[]
    documentTypes: DocumentTypeModel[]
    fileData: FileDataModel[]
    wordCloudData: WordCloudData[]
    relevanceData: RelevanceChartData
    barChartData: BarChartData
  }> {
    // Legacy mock data - use ECA API methods for real data
    console.warn(
      'loadDashboardData is deprecated. Use individual ECA API methods instead.'
    )

    // Mock data - in real implementation, this would be HTTP calls
    const custodians: CustodianModel[] = [
      { id: 1, name: 'Jenson Delaney', email: '<EMAIL>' },
      { id: 2, name: 'Amaya Coffey', email: '<EMAIL>' },
      { id: 3, name: 'Habib Joyce', email: '<EMAIL>' },
      { id: 4, name: 'Lilly-Ann Roche', email: '<EMAIL>' },
      { id: 5, name: 'Giulia Haworth', email: '<EMAIL>' },
      { id: 6, name: 'Dawson Humphrey', email: '<EMAIL>' },
      { id: 7, name: 'Reilly McCullough', email: '<EMAIL>' },
    ]

    const documentTypes: DocumentTypeModel[] = [
      {
        category: 'Official Correspondence',
        count: 207410,
        subcategories: [
          { subcategory: 'Interdepartmental Emails', count: 160687 },
          { subcategory: 'Policy Discussions', count: 30934 },
          { subcategory: 'Legislative Communications', count: 14190 },
          { subcategory: 'Internal Memos', count: 613 },
          { subcategory: 'Executive Summaries', count: 139 },
          { subcategory: 'Legal Discussions', count: 847 },
        ],
      },
      {
        category: 'Public Records and FOIA Requests',
        count: 69432,
        subcategories: [
          { subcategory: 'FOIA Requests', count: 45321 },
          { subcategory: 'Public Information Requests', count: 15432 },
          { subcategory: 'Records Releases', count: 8679 },
        ],
      },
    ]

    const fileData: FileDataModel[] = [
      {
        filename: 'PDGL00001104.txt',
        file_content: null,
        summary:
          'This document is an email correspondence from Leslie Rawle to Governor Jeb Bush expressing opposition to a proposal regarding the implementation of Universal Pre-Kindergarten (UPK) by the UPK Council and the State Board of Education.',
      },
      {
        filename: 'PDGL00001105.txt',
        file_content: null,
        summary:
          'Email discussing budget allocations for education programs and their impact on state funding.',
      },
      {
        filename: 'PDGL00001106.txt',
        file_content: null,
        summary:
          'Internal memo regarding policy changes and their implementation timeline for the upcoming fiscal year.',
      },
      {
        filename: 'PDGL00001107.txt',
        file_content: null,
        summary:
          'Meeting minutes from the education committee discussing curriculum standards and assessment protocols.',
      },
      {
        filename: 'PDGL00001108.txt',
        file_content: null,
        summary:
          'Legal brief outlining compliance requirements for educational institutions under federal regulations.',
      },
      {
        filename: 'PDGL00001109.txt',
        file_content: null,
        summary:
          'Correspondence between department heads regarding resource allocation and staffing decisions.',
      },
      {
        filename: 'PDGL00001110.txt',
        file_content: null,
        summary:
          'Report on student performance metrics and recommendations for improvement strategies.',
      },
      {
        filename: 'PDGL00001111.txt',
        file_content: null,
        summary:
          'Policy document detailing new procedures for handling sensitive student information and privacy concerns.',
      },
      {
        filename: 'PDGL00001112.txt',
        file_content: null,
        summary:
          'Budget proposal for technology upgrades in educational facilities across the district.',
      },
      {
        filename: 'PDGL00001113.txt',
        file_content: null,
        summary:
          'Communication regarding partnership opportunities with local businesses and community organizations.',
      },
      {
        filename: 'PDGL00001114.txt',
        file_content: null,
        summary:
          'Analysis of enrollment trends and demographic changes affecting educational planning.',
      },
      {
        filename: 'PDGL00001115.txt',
        file_content: null,
        summary:
          'Guidelines for implementing new safety protocols and emergency procedures in schools.',
      },
      {
        filename: 'PDGL00001116.txt',
        file_content: null,
        summary:
          'Review of teacher evaluation processes and professional development requirements.',
      },
      {
        filename: 'PDGL00001117.txt',
        file_content: null,
        summary:
          'Documentation of special education services and individualized education program updates.',
      },
      {
        filename: 'PDGL00001118.txt',
        file_content: null,
        summary:
          'Strategic plan for improving graduation rates and college readiness among students.',
      },
    ]

    const wordCloudData: WordCloudData[] = [
      {
        text: 'adoption',
        weight: 15,
        color: '#6366f1',
        tooltip: 'Adoption proceedings',
      },
      {
        text: 'kearney',
        weight: 14,
        color: '#8b5cf6',
        tooltip: 'Kearney case',
      },
      { text: 'foster', weight: 13, color: '#a855f7', tooltip: 'Foster care' },
      {
        text: 'regier',
        weight: 12,
        color: '#c084fc',
        tooltip: 'Regier family',
      },
      {
        text: 'dependency',
        weight: 11,
        color: '#ddd6fe',
        tooltip: 'Dependency matters',
      },
      { text: 'brian', weight: 10, color: '#f3e8ff', tooltip: 'Brian case' },
      {
        text: 'visitation',
        weight: 10,
        color: '#6366f1',
        tooltip: 'Visitation rights',
      },
      {
        text: 'macdonald',
        weight: 9,
        color: '#8b5cf6',
        tooltip: 'MacDonald case',
      },
      { text: 'heather', weight: 9, color: '#a855f7', tooltip: 'Heather case' },
      { text: 'dci', weight: 8, color: '#c084fc', tooltip: 'DCI involvement' },
      { text: 'tamara', weight: 8, color: '#ddd6fe', tooltip: 'Tamara case' },
      {
        text: 'gal',
        weight: 7,
        color: '#f3e8ff',
        tooltip: 'Guardian ad litem',
      },
      { text: 'stefani', weight: 7, color: '#6366f1', tooltip: 'Stefani case' },
      {
        text: 'paternal',
        weight: 6,
        color: '#8b5cf6',
        tooltip: 'Paternal rights',
      },
      {
        text: 'maternal',
        weight: 6,
        color: '#a855f7',
        tooltip: 'Maternal rights',
      },
      {
        text: 'custody',
        weight: 6,
        color: '#c084fc',
        tooltip: 'Custody arrangements',
      },
      {
        text: 'placement',
        weight: 5,
        color: '#ddd6fe',
        tooltip: 'Child placement',
      },
      {
        text: 'termination',
        weight: 5,
        color: '#f3e8ff',
        tooltip: 'Parental rights termination',
      },
      {
        text: 'reunification',
        weight: 4,
        color: '#6366f1',
        tooltip: 'Family reunification',
      },
      {
        text: 'permanency',
        weight: 4,
        color: '#8b5cf6',
        tooltip: 'Permanency planning',
      },
      {
        text: 'guardian',
        weight: 4,
        color: '#a855f7',
        tooltip: 'Legal guardian',
      },
      {
        text: 'court',
        weight: 4,
        color: '#c084fc',
        tooltip: 'Court proceedings',
      },
      {
        text: 'hearing',
        weight: 3,
        color: '#ddd6fe',
        tooltip: 'Court hearing',
      },
      {
        text: 'petition',
        weight: 3,
        color: '#f3e8ff',
        tooltip: 'Legal petition',
      },
      {
        text: 'welfare',
        weight: 3,
        color: '#6366f1',
        tooltip: 'Child welfare',
      },
      {
        text: 'services',
        weight: 3,
        color: '#8b5cf6',
        tooltip: 'Social services',
      },
      {
        text: 'assessment',
        weight: 2,
        color: '#a855f7',
        tooltip: 'Case assessment',
      },
      {
        text: 'investigation',
        weight: 2,
        color: '#c084fc',
        tooltip: 'Investigation',
      },
      { text: 'report', weight: 2, color: '#ddd6fe', tooltip: 'Case report' },
      {
        text: 'documentation',
        weight: 1,
        color: '#f3e8ff',
        tooltip: 'Case documentation',
      },
    ]

    const relevanceData: RelevanceChartData = {
      values: [40951, 28816, 12345, 8500, 3200, 1800],
      labels: [
        'Relevant',
        'Not Relevant',
        'Potentially Relevant',
        'Likely Relevant',
        'No Content',
        'Tech Issue',
      ],
      colors: [
        '#ff00ff',
        '#6305ff',
        '#0084ff',
        '#1100ff',
        '#0fe5b7',
        '#00d0ff',
      ],
    }

    const barChartData: BarChartData = {
      categories: [
        'Profanity',
        'Anger',
        'Racism',
        'Threats',
        'Harassment',
        'Discrimination',
        'Bullying',
      ],
      values: [350, 280, 200, 150, 120, 80, 60],
    }

    return of({
      custodians,
      documentTypes,
      fileData,
      wordCloudData,
      relevanceData,
      barChartData,
    })
  }

  /**
   * Fetch ECA Relevance data
   * @param projectId - The project ID
   * @param requestModel - The ECA dashboard request model
   */
  public fetchEcaRelevance(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): Observable<ResponseModel> {
    console.log('ECA Relevance Request:', {
      url: `${this._apiUrl}ai/project/${projectId}/eca-relevance`,
      payload: requestModel,
    })
    return this.httpClient.post<ResponseModel>(
      `${this._apiUrl}ai/project/${projectId}/eca-relevance`,
      requestModel
    )
  }

  /**
   * Fetch ECA Document Types data
   * @param projectId - The project ID
   * @param requestModel - The ECA dashboard request model
   */
  public fetchEcaDocumentTypes(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): Observable<ResponseModel> {
    console.log('ECA Document Types Request:', {
      url: `${this._apiUrl}ai/project/${projectId}/eca-doctype`,
      payload: requestModel,
    })
    return this.httpClient.post<ResponseModel>(
      `${this._apiUrl}ai/project/${projectId}/eca-doctype`,
      requestModel
    )
  }

  /**
   * Fetch ECA Topics data
   * @param projectId - The project ID
   * @param requestModel - The ECA dashboard request model
   */
  public fetchEcaTopics(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): Observable<ResponseModel> {
    console.log('ECA Topics Request:', {
      url: `${this._apiUrl}ai/project/${projectId}/eca-topic`,
      payload: requestModel,
    })
    return this.httpClient.post<ResponseModel>(
      `${this._apiUrl}ai/project/${projectId}/eca-topic`,
      requestModel
    )
  }

  /**
   * Fetch ECA Word Cloud data
   * @param projectId - The project ID
   * @param requestModel - The ECA dashboard request model
   */
  public fetchEcaWordCloud(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this._apiUrl}ai/project/${projectId}/word-cloud`,
      requestModel
    )
  }

  /**
   * Fetch ECA Inappropriate Content data
   * @param projectId - The project ID
   * @param requestModel - The ECA dashboard request model
   */
  public fetchEcaInappropriateContent(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this._apiUrl}ai/project/${projectId}/results/eca-csv`,
      requestModel
    )
  }

  /**
   * Helper method to build ECA Dashboard Request Model
   * @param dashboardType - The type of dashboard data to fetch
   * @param searchTempTable - The search temp table from search response
   * @param selectedFileIds - Array of selected file IDs
   * @param unSelectedFileIds - Array of unselected file IDs
   * @param isBatchSelection - Whether batch selection is enabled
   */
  public buildEcaRequestModel(
    dashboardType: ECADashboardRequestModel['dashboardType'],
    searchTempTable: string,
    selectedFileIds: number[],
    unSelectedFileIds: number[],
    isBatchSelection: boolean
  ): ECADashboardRequestModel {
    return {
      dashboardType,
      searchTempTable,
      selectedFileIds,
      unSelectedFileIds,
      isBatchSelection,
    }
  }

  public downloadCSV(
    projectId: number,
    requestModel: ECADashboardRequestModel
  ): Observable<Blob> {
    return this.httpClient.post(
      `${this._apiUrl}ai/project/${projectId}/results/eca-csv`,
      requestModel,
      {
        responseType: 'blob',
      }
    )
  }
}
