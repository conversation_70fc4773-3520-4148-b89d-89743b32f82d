import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import { AIState, AI_FEATURE_KEY } from './ai.reducer'

export const getAiState = createFeatureSelector<AIState>(AI_FEATURE_KEY)

export const getStateFromAiStore = <T extends keyof AIState>(
  stateKey: T
): MemoizedSelector<object, AIState[T], unknown> =>
  createSelector(getAiState, (state: AIState) => state[stateKey])

// ECI Dashboard Selectors
export const getEciDashboardState = createSelector(
  getAiState,
  (state: AIState) => state.eciDashboard
)

// UI State Selectors
export const getEciIsFocusedSectionOpened = createSelector(
  getEciDashboardState,
  (eciState) => eciState.isFocusedSectionOpened
)

export const getEciIsParentData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.isParentData
)

export const getEciShowDetails = createSelector(
  getEciDashboardState,
  (eciState) => eciState.showDetails
)

export const getEciShowFilterPopup = createSelector(
  getEciDashboardState,
  (eciState) => eciState.showFilterPopup
)

export const getEciShowCustodianFilters = createSelector(
  getEciDashboardState,
  (eciState) => eciState.showCustodianFilters
)

// Data Selectors
export const getEciCustodians = createSelector(
  getEciDashboardState,
  (eciState) => eciState.custodians
)

export const getEciDocumentTypes = createSelector(
  getEciDashboardState,
  (eciState) => eciState.documentTypes
)

export const getEciFileData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.fileData
)

export const getEciWordCloudData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.wordCloudData
)

export const getEciRelevanceData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.relevanceData
)

export const getEciBarChartData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.barChartData
)

export const getEciTableData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.tableData
)

export const getEciSelectedDocuments = createSelector(
  getEciDashboardState,
  (eciState) => eciState.selectedDocuments
)

export const getEciTotalDocuments = createSelector(
  getEciDashboardState,
  (eciState) => eciState.totalDocuments
)

export const getEciPaginationState = createSelector(
  getEciDashboardState,
  (eciState) => eciState.paginationState
)

// Computed Selectors
export const getEciPagedFileData = createSelector(
  getEciFileData,
  getEciPaginationState,
  (fileData, paginationState) => {
    const startIndex =
      (paginationState.currentPage - 1) * paginationState.pageSize
    const endIndex = startIndex + paginationState.pageSize
    return fileData.slice(startIndex, endIndex)
  }
)

export const getEciSortedDocumentTypes = createSelector(
  getEciDocumentTypes,
  (documentTypes) => [...documentTypes].sort((a, b) => b.count - a.count)
)

// ECA API Selectors
export const getEcaRelevanceSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaRelevanceSuccess
)

export const getEcaRelevanceError = createSelector(
  getAiState,
  (state: AIState) => state.ecaRelevanceError
)

export const getIsEcaRelevanceLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaRelevanceLoading
)

export const getEcaDocumentTypesSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaDocumentTypesSuccess
)

export const getEcaDocumentTypesError = createSelector(
  getAiState,
  (state: AIState) => state.ecaDocumentTypesError
)

export const getIsEcaDocumentTypesLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaDocumentTypesLoading
)

export const getEcaTopicsRelevantSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaTopicsRelevantSuccess
)

export const getEcaTopicsRelevantError = createSelector(
  getAiState,
  (state: AIState) => state.ecaTopicsRelevantError
)

export const getIsEcaTopicsRelevantLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaTopicsRelevantLoading
)

export const getEcaTopicsNonRelevantSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaTopicsNonRelevantSuccess
)

export const getEcaTopicsNonRelevantError = createSelector(
  getAiState,
  (state: AIState) => state.ecaTopicsNonRelevantError
)

export const getIsEcaTopicsNonRelevantLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaTopicsNonRelevantLoading
)

// ECA Word Cloud Selectors
export const getEcaWordCloudSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaWordCloudSuccess
)

export const getEcaWordCloudError = createSelector(
  getAiState,
  (state: AIState) => state.ecaWordCloudError
)

export const getIsEcaWordCloudLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaWordCloudLoading
)

// ECA Inappropriate Content Selectors
export const getEcaInappropriateContentSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaInappropriateContentSuccess
)

export const getEcaInappropriateContentError = createSelector(
  getAiState,
  (state: AIState) => state.ecaInappropriateContentError
)

export const getIsEcaInappropriateContentLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaInappropriateContentLoading
)

// Chart-specific drill-down selectors
export const getActiveChartType = createSelector(
  getEciDashboardState,
  (eciState) => eciState.activeChartType
)

export const getChartDrillDownStates = createSelector(
  getEciDashboardState,
  (eciState) => eciState.chartDrillDownStates
)

export const getChartDrillDownState = (chartType: string): any =>
  createSelector(
    getChartDrillDownStates,
    (chartStates: any) => chartStates[chartType as keyof typeof chartStates]
  )

export const getChartCurrentData = (chartType: string): any =>
  createSelector(
    getChartDrillDownState(chartType),
    (chartState: any) => chartState?.currentData || []
  )

export const getChartTableData = (chartType: string): any =>
  createSelector(
    getChartDrillDownState(chartType),
    (chartState: any) => chartState?.tableData || []
  )

export const getChartIsExpanded = (chartType: string): any =>
  createSelector(
    getChartDrillDownState(chartType),
    (chartState: any) => chartState?.isExpanded || false
  )

export const getChartSelectedNode = (chartType: string): any =>
  createSelector(
    getChartDrillDownState(chartType),
    (chartState: any) => chartState?.selectedNode
  )

export const getChartCurrentLevel = (chartType: string): any =>
  createSelector(
    getChartDrillDownState(chartType),
    (chartState: any) => chartState?.currentLevel || 0
  )

export const getChartCanGoBack = (chartType: string): any =>
  createSelector(
    getChartDrillDownState(chartType),
    (chartState: any) => (chartState?.drillDownHistory?.length || 0) > 1
  )
