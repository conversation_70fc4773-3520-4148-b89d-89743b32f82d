import {
  Component,
  inject,
  computed,
  effect,
  input,
  signal,
  PLATFORM_ID,
} from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { calculateSunburstData } from './helpers'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { VerticalLegendsComponent } from '../vertical-legends/vertical-legends.component'
import { NgClass, NgIf, isPlatformBrowser } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  AiFacade,
  ECADashboardType,
  SunburstChartType,
} from '@venio/data-access/ai'
import { relevanceChartColors, otherChartColors } from '../../constants/colors'
import {
  formatParentData,
  formatChildData,
} from '../data-table-for-focused-section/helpers'
import { CenterTextComponent } from '../center-text/center-text.component'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { IconComponent } from '@progress/kendo-angular-icons'

@Component({
  selector: 'venio-sunburst',
  standalone: true,
  imports: [
    PlotlyModule,
    TitleAndDownloadComponent,
    VerticalLegendsComponent,
    NgIf,
    CenterTextComponent,
    ButtonComponent,
    IconComponent,
    NgClass,
  ],
  templateUrl: './sunburst.component.html',
  styleUrl: './sunburst.component.scss',
})
export class SunburstComponent {
  public readonly showLegend = input<boolean>(true)

  public readonly chartTitle = input<SunburstChartType>()

  public readonly isFocusedMode = input<boolean>(false)

  private readonly aiFacade = inject(AiFacade)

  private readonly platformId = inject(PLATFORM_ID)

  private readonly isBrowser = isPlatformBrowser(this.platformId)

  private readonly SunburstChartType = SunburstChartType

  public readonly dashboardType = ECADashboardType

  public readonly documentTypes = toSignal(
    this.aiFacade.selectEciSortedDocumentTypes$,
    { initialValue: [] }
  )

  // ECA API data - Document Types
  public readonly ecaDocumentTypesData = toSignal(
    this.aiFacade.selectEcaDocumentTypesSuccess$,
    { initialValue: null }
  )

  // ECA API data - Topics Relevant
  public readonly ecaTopicsRelevantData = toSignal(
    this.aiFacade.selectEcaTopicsRelevantSuccess$,
    { initialValue: null }
  )

  // ECA API data - Topics Non-Relevant
  public readonly ecaTopicsNonRelevantData = toSignal(
    this.aiFacade.selectEcaTopicsNonRelevantSuccess$,
    { initialValue: null }
  )

  // ECA API data - Relevance
  public readonly ecaRelevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  // Computed signal for current chart data based on chart type
  public readonly currentChartData = computed(() => {
    try {
      const chartTitle = this.chartTitle()
      switch (chartTitle) {
        case this.SunburstChartType.DocumentTypes: {
          const docTypesData = this.ecaDocumentTypesData()
          if (
            docTypesData?.data?.documentTypes &&
            Array.isArray(docTypesData.data.documentTypes) &&
            docTypesData.data.documentTypes.length > 0
          ) {
            return this.#transformEcaDocumentTypes(
              docTypesData.data.documentTypes
            )
          }
          // Fallback to legacy data only for Document Types
          const legacyDocTypes = this.documentTypes()
          return Array.isArray(legacyDocTypes) && legacyDocTypes.length > 0
            ? legacyDocTypes
            : []
        }

        case this.SunburstChartType.RelevantDocuments: {
          const relevantData = this.ecaTopicsRelevantData()
          if (
            relevantData?.data?.topics &&
            Array.isArray(relevantData.data.topics) &&
            relevantData.data.topics.length > 0
          ) {
            return this.#transformEcaTopicsToDocumentTypes(
              relevantData.data.topics
            )
          }
          return []
        }

        case this.SunburstChartType.NotRelevantDocuments: {
          const nonRelevantData = this.ecaTopicsNonRelevantData()
          if (
            nonRelevantData?.data?.topics &&
            Array.isArray(nonRelevantData.data.topics) &&
            nonRelevantData.data.topics.length > 0
          ) {
            return this.#transformEcaTopicsToDocumentTypes(
              nonRelevantData.data.topics
            )
          }
          return []
        }

        case this.SunburstChartType.Relevance: {
          // For relevance chart, use actual relevance data
          const relevanceData = this.ecaRelevanceData()
          if (
            relevanceData?.data &&
            Array.isArray(relevanceData.data) &&
            relevanceData.data.length > 0
          ) {
            return this.#transformEcaRelevanceData(relevanceData.data)
          }
          return []
        }

        default: {
          // Default fallback only for Document Types
          const defaultDocTypes = this.documentTypes()
          return Array.isArray(defaultDocTypes) && defaultDocTypes.length > 0
            ? defaultDocTypes
            : []
        }
      }
    } catch (error) {
      console.error('Error computing current chart data:', error)
      return []
    }
  })

  // Global state for table data management (still needed for non-focused mode)
  public readonly globalIsParentData = toSignal(
    this.aiFacade.selectEciIsParentData$,
    { initialValue: true }
  )

  // Chart-specific state signals for all chart types (created outside reactive context)
  public readonly documentTypesStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(
      this.SunburstChartType.DocumentTypes
    ),
    { initialValue: null }
  )

  public readonly relevantDocumentsStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(
      this.SunburstChartType.RelevantDocuments
    ),
    { initialValue: null }
  )

  public readonly notRelevantDocumentsStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(
      this.SunburstChartType.NotRelevantDocuments
    ),
    { initialValue: null }
  )

  public readonly relevanceStateSignal = toSignal(
    this.aiFacade.selectChartDrillDownState$(this.SunburstChartType.Relevance),
    { initialValue: null }
  )

  // Chart-specific can go back signals for all chart types
  public readonly documentTypesCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(this.SunburstChartType.DocumentTypes),
    { initialValue: false }
  )

  public readonly relevantDocumentsCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(
      this.SunburstChartType.RelevantDocuments
    ),
    { initialValue: false }
  )

  public readonly notRelevantDocumentsCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(
      this.SunburstChartType.NotRelevantDocuments
    ),
    { initialValue: false }
  )

  public readonly relevanceCanGoBackSignal = toSignal(
    this.aiFacade.selectChartCanGoBack$(this.SunburstChartType.Relevance),
    { initialValue: false }
  )

  // Computed properties that select the correct state based on chart type
  public readonly chartDrillDownState = computed(() => {
    if (this.isFocusedMode()) {
      const chartTitle = this.chartTitle()
      switch (chartTitle) {
        case this.SunburstChartType.DocumentTypes:
          return this.documentTypesStateSignal()
        case this.SunburstChartType.RelevantDocuments:
          return this.relevantDocumentsStateSignal()
        case this.SunburstChartType.NotRelevantDocuments:
          return this.notRelevantDocumentsStateSignal()
        case this.SunburstChartType.Relevance:
          return this.relevanceStateSignal()
        default:
          return this.documentTypesStateSignal()
      }
    }
    return null
  })

  public readonly chartIsExpanded = computed(() => {
    // Use the same expansion state logic for both modes
    return this.isChartExpandedSignal()
  })

  public readonly chartCanGoBack = computed(() => {
    if (this.isFocusedMode()) {
      const chartTitle = this.chartTitle()
      switch (chartTitle) {
        case this.SunburstChartType.DocumentTypes:
          return this.documentTypesCanGoBackSignal()
        case this.SunburstChartType.RelevantDocuments:
          return this.relevantDocumentsCanGoBackSignal()
        case this.SunburstChartType.NotRelevantDocuments:
          return this.notRelevantDocumentsCanGoBackSignal()
        case this.SunburstChartType.Relevance:
          return this.relevanceCanGoBackSignal()
        default:
          return this.documentTypesCanGoBackSignal()
      }
    }
    return false
  })

  public ecaDashboadType = computed(() => {
    const chartTitle = this.chartTitle()
    switch (chartTitle) {
      case this.SunburstChartType.DocumentTypes:
        return ECADashboardType.DocumentType
      case this.SunburstChartType.RelevantDocuments:
        return ECADashboardType.Topic_Relevant
      case this.SunburstChartType.NotRelevantDocuments:
        return ECADashboardType.Topic_NonRelevant
      default:
    }
  })

  // Chart-specific expanded state (independent for each chart instance in non-focused mode)
  private readonly isChartExpandedSignal = signal(false)

  // Computed property to determine colors based on chart type
  public readonly chartColors = computed(() => {
    const chartTitle = this.chartTitle()
    return chartTitle === SunburstChartType.Relevance
      ? relevanceChartColors
      : otherChartColors
  })

  // Signal to determine if chart data is ready for rendering
  public readonly isChartDataReady = computed(() => {
    const chartData = this.currentChartData()
    return chartData && Array.isArray(chartData) && chartData.length > 0
  })

  // Signal to determine if chart should be rendered
  public readonly shouldRenderChart = computed(() => {
    return this.isBrowser && this.isChartDataReady()
  })

  // Chart data properties
  public parentLabels: any = []

  public childrenLabels: any = []

  public chartOneTotal = 0

  public chartOneSubTotal: number[] = []

  public chartOnePercents: number[] | undefined = []

  public chartOneChildPercents: number[][] | undefined = []

  public allCountsOne: number[] = []

  public allValsOne: number[] = []

  public formattedCounts: string[] = []

  // Internal state signals for chart data
  private readonly chartDataSignal = signal<any[]>([])

  private readonly chartLayoutSignal = signal<any>({})

  private readonly chartConfigSignal = signal<any>({})

  // Computed signals for reactive Plotly integration
  public readonly graph = computed(() => ({
    data: this.chartDataSignal(),
    layout: this.chartLayoutSignal(),
  }))

  public readonly config = computed(() => this.chartConfigSignal())

  public labels: string[] = []

  public parents: string[] = ['Total']

  // Store original colors for highlighting reset
  private originalColors: string[] = []

  // Store original values for smooth expansion/collapse
  private originalValues: number[] = []

  // Track last chart state to prevent unnecessary rebuilds
  private lastChartLevel = 0

  private lastChartExpanded = false

  constructor() {
    // Effect to automatically update chart when data changes
    effect(
      () => {
        const chartData = this.currentChartData()
        if (chartData && chartData.length > 0) {
          // Initialize chart data the same way for both modes
          this.initializeChartData(chartData)
        } else {
          this.#initializeEmptyChart()
        }
      },
      { allowSignalWrites: true }
    )

    // Remove focused mode specific drill-down effect - use same logic for both modes
  }

  private initializeChartData(documentTypes: any[]): void {
    try {
      if (
        !documentTypes ||
        !Array.isArray(documentTypes) ||
        documentTypes.length === 0
      ) {
        console.warn(
          'Invalid or empty document types data provided to initializeChartData'
        )
        this.#initializeEmptyChart()
        return
      }

      const sortedDocuTypes = [...documentTypes].sort((a, b) => {
        const countA = a?.count || 0
        const countB = b?.count || 0
        return countB - countA
      })

      // Check if this is relevance data (no subcategories)
      const isRelevanceData = sortedDocuTypes.every(
        (item) => !item.subcategories || item.subcategories.length === 0
      )

      if (isRelevanceData) {
        this.#initializeRelevanceChart(sortedDocuTypes)
        return
      }

      const calculationResult = calculateSunburstData(sortedDocuTypes)
      if (!calculationResult) {
        console.warn('Failed to calculate sunburst data')
        this.#initializeEmptyChart()
        return
      }

      const {
        parentLabels,
        childrenLabels,
        chartOneTotal,
        chartOneSubTotal,
        chartOnePercents,
        chartOneChildPercents,
        allCountsOne,
        allValsOne,
        formattedCounts,
      } = calculationResult

      this.parentLabels = parentLabels
      this.childrenLabels = childrenLabels
      this.chartOneTotal = chartOneTotal
      this.chartOneSubTotal = chartOneSubTotal
      this.chartOnePercents = chartOnePercents
      this.chartOneChildPercents = chartOneChildPercents
      this.allCountsOne = allCountsOne
      this.allValsOne = allValsOne
      this.formattedCounts = formattedCounts

      this.labels = ['Total']
      this.parents = ['']

      const parentsLen = this.parentLabels.length
      this.labels.push(...this.parentLabels)
      this.parents.push(...Array.from({ length: parentsLen }, () => 'Total'))

      for (let i = 0; i < this.childrenLabels.length; i++) {
        this.labels.push(...this.childrenLabels[i])
        const len = this.childrenLabels[i].length
        this.parents.push(
          ...Array.from({ length: len }, () => this.parentLabels[i])
        )
      }

      // structure for the sunburst chart for angular - adjusting from React version
      const updatedVals = [...this.allValsOne]
      this.parents.forEach((parent, idx) => {
        if (parent === 'Total' || idx === 0) {
          updatedVals[idx] = 0
        }
      })

      // Store original values for smooth expansion/collapse
      this.originalValues = [...updatedVals]

      // Use default hole size during initialization - hole size will be updated separately when needed
      const holeSize = 0.5

      // Generate colors for the chart based on chart type
      const colors = this.chartColors()
      const chartColors = this.#generateChartColors(this.labels.length, colors)

      // Update chart data signal
      this.chartDataSignal.set([
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: updatedVals,
          hole: holeSize,
          textinfo: 'label',
          marker: {
            colors: chartColors,
            line: { width: 0.5, color: '#ffffff' }, // Reduced gap by 40% - smaller borders between segments
          },
          leaf: { opacity: 0.9 }, // Slightly transparent leaves for better separation
        },
      ])

      // Update chart layout signal
      this.chartLayoutSignal.set({
        autosize: true,
        automargin: false,
        branchvalues: 'remainder', // Required for donut
        margin: { t: 20, r: 20, b: 20, l: 20 },
        height: 400,
        width: undefined, // Let it auto-size width
        showlegend: false,
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
      })
      // Update chart config signal
      this.chartConfigSignal.set({
        responsive: true,
        displayModeBar: false,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toImage',
          'sendDataToCloud',
          'editInChartStudio',
          'zoom2d',
          'select2d',
          'pan2d',
          'lasso2d',
          'autoScale2d',
          'resetScale2d',
        ],
        // Ensure proper resizing behavior
        autosizable: true,
        fillFrame: false,
        frameMargins: 0,
        // Enable interactive cursor
        staticPlot: false,
        scrollZoom: false,
        doubleClick: false,
        showTips: false,
        editable: false,
      })

      // Store original colors for highlighting functionality
      this.originalColors = [...chartColors]

      // Update hole size after chart initialization to reflect current expanded state
      setTimeout(() => {
        this.#updateChartHoleSize()
      }, 50)
    } catch (error) {
      console.error('Error initializing chart data:', error)
      this.#initializeEmptyChart()
    }
  }

  #generateChartColors(labelCount: number, colorScheme: string[]): string[] {
    const colors: string[] = []
    // Skip index 0 (transparent) and use colors starting from index 1
    const availableColors = colorScheme.slice(1)

    // Check if this is relevance chart for special handling
    const isRelevanceChart = this.chartTitle() === SunburstChartType.Relevance

    if (isRelevanceChart) {
      // For relevance charts, use sequential order as specified
      for (let i = 0; i < labelCount; i++) {
        if (i === 0) {
          colors.push('transparent')
        } else {
          const colorIndex = (i - 1) % availableColors.length
          colors.push(availableColors[colorIndex])
        }
      }
    } else {
      // For other charts, implement proper parent-child color relationship
      const parentColorMap = new Map<string, string>()
      const usedParentColors = new Set<string>()
      let parentColorIndex = 0

      // Build a more robust parent-child mapping
      // First pass: identify all parent elements and assign colors
      const parentIndices = new Set<number>()

      for (let i = 0; i < labelCount; i++) {
        if (i === 0) {
          // Root element
          colors.push('transparent')
          continue
        }

        const label = this.labels[i]
        const parent = this.parents[i]
        const isChildElement = label && label.match(/^\d+:\s/)

        if (!isChildElement && parent === 'Total') {
          // This is a parent element (inner ring)
          parentIndices.add(i)

          // Assign a unique color
          let baseColor =
            availableColors[parentColorIndex % availableColors.length]

          // Ensure no duplicate colors for parent elements
          let attempts = 0
          while (
            usedParentColors.has(baseColor) &&
            attempts < availableColors.length
          ) {
            parentColorIndex++
            baseColor =
              availableColors[parentColorIndex % availableColors.length]
            attempts++
          }

          colors.push(baseColor)
          parentColorMap.set(label, baseColor)
          usedParentColors.add(baseColor)
          parentColorIndex++
        } else {
          // Placeholder for child elements, will be filled in second pass
          colors.push('')
        }
      }

      // Second pass: assign child colors based on their parent's color
      for (let i = 1; i < labelCount; i++) {
        if (parentIndices.has(i)) {
          // Already processed in first pass
          continue
        }

        const label = this.labels[i]
        const parent = this.parents[i]
        const isChildElement = label && label.match(/^\d+:\s/)

        if (isChildElement) {
          // This is a child element (outer ring) - use lighter shade of parent's color
          const parentColor = parentColorMap.get(parent)

          if (parentColor) {
            const lighterColor = this.#getLighterShade(parentColor)
            colors[i] = lighterColor
          } else {
            // Fallback: use a default color
            colors[i] = availableColors[i % availableColors.length]
          }
        } else {
          // This might be a parent that wasn't caught in the first pass
          // Assign it a unique color
          let baseColor =
            availableColors[parentColorIndex % availableColors.length]

          let attempts = 0
          while (
            usedParentColors.has(baseColor) &&
            attempts < availableColors.length
          ) {
            parentColorIndex++
            baseColor =
              availableColors[parentColorIndex % availableColors.length]
            attempts++
          }

          colors[i] = baseColor
          parentColorMap.set(label, baseColor)
          usedParentColors.add(baseColor)
          parentColorIndex++
        }
      }
    }

    return colors
  }

  #getLighterShade(hexColor: string): string {
    // Convert hex to RGB
    const hex = hexColor.replace('#', '')
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)

    // Lighten by adding 30% towards white (255) for better contrast
    const lightenFactor = 0.3
    const newR = Math.round(r + (255 - r) * lightenFactor)
    const newG = Math.round(g + (255 - g) * lightenFactor)
    const newB = Math.round(b + (255 - b) * lightenFactor)

    // Convert back to hex
    const toHex = (n: number): string => {
      const hex = n.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`
  }

  #generateExpandedChartColors(
    selectedNode: any,
    labels: string[],
    colorScheme: string[]
  ): string[] {
    const colors: string[] = []
    const availableColors = colorScheme.slice(1) // Skip transparent color

    // Find the original color of the selected node from the current chart
    const selectedNodeColor = this.#findOriginalNodeColor(selectedNode)
    const parentColor = selectedNodeColor || availableColors[0] || '#1f77b4'

    // For expanded charts:
    // Index 0: 'Total' - transparent
    // Index 1: Parent node (inner ring) - original color of selected node
    // Index 2+: Child nodes (outer ring) - lighter shade of parent color

    for (let i = 0; i < labels.length; i++) {
      if (i === 0) {
        // Root 'Total' - transparent
        colors.push('transparent')
      } else if (i === 1) {
        // Parent node (inner ring) - use original color of selected node
        colors.push(parentColor)
      } else {
        // Child nodes (outer ring) - use lighter shade of parent color
        const lighterColor = this.#getLighterShade(parentColor)
        colors.push(lighterColor)
      }
    }

    return colors
  }

  #findOriginalNodeColor(selectedNode: any): string | null {
    try {
      // Find the color of the selected node from the original chart
      if (
        !selectedNode ||
        !this.originalColors ||
        this.originalColors.length === 0
      ) {
        return null
      }

      const nodeName = selectedNode.category || selectedNode.name
      if (!nodeName) {
        return null
      }

      // Find the index of this node in the current labels
      const nodeIndex = this.labels.findIndex((label) => label === nodeName)

      if (nodeIndex !== -1 && nodeIndex < this.originalColors.length) {
        const originalColor = this.originalColors[nodeIndex]
        console.log(`Found original color for ${nodeName}:`, originalColor)
        return originalColor
      }

      console.warn(`Could not find original color for node: ${nodeName}`)
      return null
    } catch (error) {
      console.error('Error finding original node color:', error)
      return null
    }
  }

  #initializeEmptyChart(): void {
    this.labels = ['No Data']
    this.parents = ['']

    // Update chart data signal
    this.chartDataSignal.set([
      {
        type: 'sunburst',
        labels: this.labels,
        parents: this.parents,
        values: [1],
        hole: 0.5,
        textinfo: 'label',
        marker: {
          colors: ['#e5e7eb'],
          line: { width: 0.5, color: '#ffffff' },
        },
        leaf: { opacity: 0.9 },
      },
    ])

    // Update chart layout signal
    this.chartLayoutSignal.set({
      autosize: true,
      automargin: false,
      branchvalues: 'remainder',
      margin: { t: 20, r: 20, b: 20, l: 20 },
      height: 400,
      width: undefined,
      showlegend: false,
      paper_bgcolor: 'rgba(0,0,0,0)',
      plot_bgcolor: 'rgba(0,0,0,0)',
    })
    // Update chart config signal
    this.chartConfigSignal.set({
      responsive: true,
      displayModeBar: false,
      displaylogo: false,
      autosizable: true,
      fillFrame: false,
      frameMargins: 0,
      // Enable interactive cursor
      staticPlot: false,
      scrollZoom: false,
      doubleClick: false,
      showTips: false,
      editable: false,
    })
  }

  #rebuildChartForDrillDown(
    drillDownState: any,
    originalChartData: any[]
  ): void {
    try {
      console.log('Rebuilding chart for drill-down state:', drillDownState)

      if (drillDownState.isExpanded && drillDownState.selectedNode) {
        // Chart is expanded - show the selected node's children
        const selectedNode = drillDownState.selectedNode
        console.log('Building expanded chart for node:', selectedNode)

        if (
          selectedNode.subcategories &&
          selectedNode.subcategories.length > 0
        ) {
          this.#buildExpandedChart(selectedNode)
        } else {
          console.warn('Selected node has no subcategories to expand')
        }
      } else {
        // Chart is collapsed - show parent data
        console.log('Building collapsed chart with original data')
        this.initializeChartData(originalChartData)
      }
    } catch (error) {
      console.error('Error rebuilding chart for drill-down:', error)
    }
  }

  #buildExpandedChart(selectedNode: any): void {
    try {
      console.log('Expanding chart for selected node:', selectedNode)

      if (
        this.isBrowser &&
        typeof window !== 'undefined' &&
        (window as any).Plotly
      ) {
        const plotlyElement = document.querySelector(
          'plotly-plot div[id^="plotly-div-"]'
        )

        if (plotlyElement) {
          // Create new values array that emphasizes the selected node and its children
          const selectedParent = selectedNode.category || selectedNode.name
          const newValues = this.#createExpandedValues(selectedParent)

          // Use Plotly.animate to smoothly transition the values
          ;(window as any).Plotly.animate(
            plotlyElement,
            {
              data: [
                {
                  values: newValues,
                  hole: 0.4,
                },
              ],
            },
            {
              transition: {
                duration: 400,
                easing: 'cubic-in-out',
              },
              frame: {
                duration: 400,
              },
            }
          )
        }
      }
    } catch (error) {
      console.error('Error expanding chart:', error)
    }
  }

  #createExpandedValues(selectedParent: string): number[] {
    const newValues = [...this.originalValues]
    const shrinkFactor = 0.1 // Make non-selected segments 10% of original size
    const expandFactor = 3.0 // Make selected segments 3x larger

    for (let i = 0; i < this.labels.length; i++) {
      const label = this.labels[i]
      const parent = this.parents[i]

      // Skip the root node (Total)
      if (i === 0) continue

      // If this is the selected parent or its child, expand it
      if (label === selectedParent || parent === selectedParent) {
        newValues[i] = this.originalValues[i] * expandFactor
      } else {
        // Shrink all other segments
        newValues[i] = this.originalValues[i] * shrinkFactor
      }
    }

    return newValues
  }

  #restoreFullChart(): void {
    try {
      console.log('Restoring full chart')

      if (
        this.isBrowser &&
        typeof window !== 'undefined' &&
        (window as any).Plotly
      ) {
        const plotlyElement = document.querySelector(
          'plotly-plot div[id^="plotly-div-"]'
        )

        if (plotlyElement) {
          // Use Plotly.animate to restore original values
          ;(window as any).Plotly.animate(
            plotlyElement,
            {
              data: [
                {
                  values: this.originalValues,
                  hole: 0.5,
                },
              ],
            },
            {
              transition: {
                duration: 400,
                easing: 'cubic-in-out',
              },
              frame: {
                duration: 400,
              },
            }
          )
        }
      }
    } catch (error) {
      console.error('Error restoring full chart:', error)
    }
  }

  #forceChartRedraw(): void {
    try {
      if (
        this.isBrowser &&
        typeof window !== 'undefined' &&
        (window as any).Plotly
      ) {
        const plotlyElement = document.querySelector(
          'plotly-plot div[id^="plotly-div-"]'
        )
        if (plotlyElement) {
          console.log('Forcing chart redraw')
          ;(window as any).Plotly.redraw(plotlyElement)
        } else {
          console.warn('Plotly element not found for redraw')
        }
      }
    } catch (error) {
      console.error('Error forcing chart redraw:', error)
    }
  }

  #animateChartTransition(newData: any, newLayout: any): void {
    try {
      if (
        this.isBrowser &&
        typeof window !== 'undefined' &&
        (window as any).Plotly
      ) {
        const plotlyElement = document.querySelector(
          'plotly-plot div[id^="plotly-div-"]'
        )
        if (plotlyElement) {
          console.log('Animating chart transition')
          // Use Plotly.animate for smooth transitions
          ;(window as any).Plotly.animate(
            plotlyElement,
            {
              data: newData,
              layout: newLayout,
            },
            {
              transition: {
                duration: 300,
                easing: 'cubic-in-out',
              },
              frame: {
                duration: 300,
                redraw: false,
              },
            }
          )
        } else {
          console.warn('Plotly element not found for animation')
        }
      }
    } catch (error) {
      console.error('Error animating chart transition:', error)
      // Fallback to regular redraw
      this.#forceChartRedraw()
    }
  }

  #highlightSelectedSegment(selectedLabel: string): void {
    try {
      console.log('Highlighting selected segment:', selectedLabel)

      const graph = this.graph()
      if (!graph || !graph.data || !graph.data[0]) {
        console.warn('Chart data not available for highlighting')
        return
      }

      if (!this.originalColors || this.originalColors.length === 0) {
        console.warn('Original colors not available for highlighting')
        return
      }

      const chartData = graph.data[0]
      // Always start from original colors to avoid corruption
      const colors = [...this.originalColors]

      // Find the index of the selected label
      const selectedIndex = this.labels.findIndex(
        (label) => label === selectedLabel
      )

      if (selectedIndex === -1) {
        console.warn('Selected label not found in chart labels:', selectedLabel)
        return
      }

      // Apply highlighting: gray out all segments except selected one
      for (let i = 0; i < colors.length; i++) {
        if (i === 0) {
          // Keep root transparent
          continue
        } else if (i === selectedIndex) {
          // Keep selected segment at original color (no change)
          continue
        } else {
          // Gray out other segments with 40% opacity using original color
          const originalColor = this.originalColors[i]
          if (originalColor && originalColor !== 'transparent') {
            colors[i] = this.#applyGrayOpacity(originalColor, 0.4)
          }
        }
      }

      // Update chart colors using Plotly.restyle for smooth highlighting
      if (
        this.isBrowser &&
        typeof window !== 'undefined' &&
        (window as any).Plotly
      ) {
        const plotlyElement = document.querySelector(
          'plotly-plot div[id^="plotly-div-"]'
        )
        if (plotlyElement) {
          // Use Plotly.restyle for smooth color transition
          ;(window as any).Plotly.restyle(
            plotlyElement,
            {
              'marker.colors': [colors],
            },
            [0],
            {
              transition: {
                duration: 150,
                easing: 'cubic-in-out',
              },
            }
          )
        } else {
          // Fallback to direct update
          chartData.marker.colors = colors
          this.#forceChartRedraw()
        }
      } else {
        // Fallback when Plotly is not available
        chartData.marker.colors = colors
        this.#forceChartRedraw()
      }

      console.log('Segment highlighting applied successfully')
    } catch (error) {
      console.error('Error highlighting selected segment:', error)
    }
  }

  #applyGrayOpacity(hexColor: string, opacity: number): string {
    try {
      // Convert hex to RGB
      const hex = hexColor.replace('#', '')
      const r = parseInt(hex.substring(0, 2), 16)
      const g = parseInt(hex.substring(2, 4), 16)
      const b = parseInt(hex.substring(4, 6), 16)

      // Convert to grayscale and apply opacity
      const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b)

      return `rgba(${gray}, ${gray}, ${gray}, ${opacity})`
    } catch (error) {
      console.error('Error applying gray opacity:', error)
      return `rgba(128, 128, 128, ${opacity})` // Fallback gray
    }
  }

  #shouldRebuildChart(currentLevel: number, isExpanded: boolean): boolean {
    // Check if there's a structural change that requires chart rebuild
    const levelChanged = currentLevel !== this.lastChartLevel
    const expandedChanged = isExpanded !== this.lastChartExpanded

    // Update tracking variables
    this.lastChartLevel = currentLevel
    this.lastChartExpanded = isExpanded

    // Only rebuild if there's a structural change
    const shouldRebuild = levelChanged || expandedChanged

    console.log('Chart rebuild check:', {
      currentLevel,
      isExpanded,
      levelChanged,
      expandedChanged,
      shouldRebuild,
    })

    return shouldRebuild
  }

  public onChartClick(event: any): void {
    try {
      if (
        !event ||
        !event.points ||
        !Array.isArray(event.points) ||
        event.points.length === 0
      ) {
        console.warn('Invalid chart click event:', event)
        return
      }

      const clickedPoint = event.points[0]

      if (!clickedPoint || !clickedPoint.label) {
        console.warn('Invalid clicked point:', clickedPoint)
        return
      }

      // Get the appropriate data based on chart type
      const chartData = this.currentChartData()

      if (!chartData || !Array.isArray(chartData)) {
        console.warn('No chart data available for interaction')
        return
      }

      // Use the same click handling logic for both modes
      this.#handleNormalModeClick(clickedPoint, chartData)
    } catch (error) {
      console.error('Error handling chart click:', error)
    }
  }

  public onLegendClick(event: { legendName: string; selectedItem: any }): void {
    try {
      const { legendName, selectedItem } = event
      console.log(`Legend clicked in sunburst: ${legendName}`, selectedItem)
      console.log('isFocusedMode:', this.isFocusedMode())
      console.log('chartIsExpanded:', this.chartIsExpanded())

      // Get the appropriate data based on chart type
      const chartData = this.currentChartData()

      if (!chartData || !Array.isArray(chartData)) {
        console.warn('No chart data available for legend interaction')
        return
      }

      // Use the same legend click handling logic for both modes
      this.#handleNormalModeLegendClick(legendName, chartData)
    } catch (error) {
      console.error('Error handling legend click:', error)
    }
  }

  #handleFocusedModeClick(clickedPoint: any, chartData: any[]): void {
    const chartType = this.chartTitle()
    const currentState = this.chartDrillDownState()
    const isCurrentlyExpanded = currentState?.isExpanded || false

    // Check if this is relevance data (single layer)
    const isRelevanceData = chartData.every(
      (item) => !item.subcategories || item.subcategories.length === 0
    )

    if (isRelevanceData && clickedPoint.parent === 'Total') {
      // For relevance data, clicking a segment shows file details directly
      console.log(
        `${this.chartTitle()} - Relevance segment clicked:`,
        clickedPoint
      )
      this.aiFacade.setEciShowDetails(true)
      return
    }

    if (!isCurrentlyExpanded && clickedPoint.parent === 'Total') {
      // Parent segment clicked - drill down to child level
      const childLabel = clickedPoint.label
      const childData = chartData.find(
        (item: any) =>
          item?.category === childLabel || item?.name === childLabel
      )

      console.log(
        `${this.chartTitle()} - Focused mode child data clicked:`,
        childData
      )
      if (!childData) {
        console.warn('Child data not found for label:', childLabel)
        return
      }

      // Use facade drill-down method to manage state
      const formatted = formatChildData(childData)
      if (formatted && Array.isArray(formatted)) {
        this.aiFacade.drillDownToNextLevel(chartType, childData, formatted)
        this.aiFacade.updateChartTableData(chartType, formatted)

        // Update hole size after state change
        setTimeout(() => {
          this.#updateChartHoleSize()
        }, 50)
      }
    } else if (isCurrentlyExpanded && clickedPoint.parent === 'Total') {
      // Back to parent view - drill back
      console.log(`${this.chartTitle()} - Focused mode back to parent view`)
      this.aiFacade.drillBackToPreviousLevel(chartType)

      const formatted = formatParentData(chartData)
      if (formatted && Array.isArray(formatted)) {
        this.aiFacade.updateChartTableData(chartType, formatted)
      }

      // Update hole size after state change
      setTimeout(() => {
        this.#updateChartHoleSize()
      }, 50)
    } else {
      // Grandchild clicked - show file details
      console.log(
        `${this.chartTitle()} - Focused mode grandchild clicked:`,
        clickedPoint
      )
      // In focused mode, details are already shown in the focused section
    }
  }

  #handleNormalModeClick(clickedPoint: any, chartData: any[]): void {
    if (!this.chartIsExpanded() && clickedPoint.parent === 'Total') {
      // Parent segment clicked - show child data in table
      const childLabel = clickedPoint.label
      const childData = chartData.find(
        (item: any) =>
          item?.category === childLabel || item?.name === childLabel
      )

      console.log(`${this.chartTitle()} - Child data clicked:`, childData)
      if (!childData) {
        console.warn('Child data not found for label:', childLabel)
        return
      }

      const formatted = formatChildData(childData)
      if (formatted && Array.isArray(formatted)) {
        // Store data for both global table and focused section legend table
        this.aiFacade.storeEciTableData(formatted)
        this.aiFacade.setEciParentDataView(false)

        // Ensure focused section legend table gets the data
        const chartType = this.chartTitle()
        this.aiFacade.updateChartTableData(chartType, formatted)
        this.aiFacade.setActiveChartType(chartType)

        // Open focused section with details
        this.aiFacade.setEciShowDetails(true)
        this.aiFacade.setEciFocusedSectionOpened(true)

        // Set chart-specific expanded state and expand using opacity (no white space)
        this.isChartExpandedSignal.set(true)
        this.#buildExpandedChart(childData)
      }
    } else if (this.chartIsExpanded() && clickedPoint.parent === 'Total') {
      // Back to parent view
      console.log(`${this.chartTitle()} - Back to parent view`)
      const formatted = formatParentData(chartData)
      if (formatted && Array.isArray(formatted)) {
        // Update both global table data and focused section legend table
        this.aiFacade.storeEciTableData(formatted)
        this.aiFacade.setEciParentDataView(true)
        this.aiFacade.setEciShowDetails(false)

        // Ensure focused section legend table gets the parent data
        const chartType = this.chartTitle()
        this.aiFacade.updateChartTableData(chartType, formatted)

        // Set chart-specific collapsed state and restore all segments (no white space)
        this.isChartExpandedSignal.set(false)
        this.#restoreFullChart()
      }
    } else {
      // Grandchild clicked - show file details
      console.log(`${this.chartTitle()} - Grandchild clicked:`, clickedPoint)
      this.aiFacade.setEciShowDetails(true)
      this.aiFacade.setEciFocusedSectionOpened(true)
    }
  }

  #handleFocusedModeLegendClick(legendName: string, chartData: any[]): void {
    const currentState = this.chartDrillDownState()
    const isCurrentlyExpanded = currentState?.isExpanded || false

    if (isCurrentlyExpanded) {
      // Chart is expanded - check if this is a leaf node selection for highlighting
      const selectedSubcategory =
        currentState?.selectedNode?.subcategories?.find(
          (sub: any) => sub.subcategory === legendName
        )

      if (selectedSubcategory) {
        console.log(
          `${this.chartTitle()} - Legend selection for highlighting:`,
          legendName
        )
        this.#highlightSelectedSegment(legendName)
        return
      }
    }

    // For all other cases, just simulate a ring click - this ensures identical behavior
    const fakeClickedPoint = {
      label: legendName,
      parent: 'Total',
    }

    console.log(
      `${this.chartTitle()} - Legend click simulating chart click for:`,
      legendName
    )
    this.#handleNormalModeClick(fakeClickedPoint, chartData)
  }

  #handleNormalModeLegendClick(legendName: string, chartData: any[]): void {
    // Check if chart is expanded and this is a leaf node selection for highlighting
    if (this.chartIsExpanded()) {
      // In normal mode, check if this is a leaf node by looking at current table data
      // If it's a leaf node, apply highlighting instead of drilling
      console.log(
        `${this.chartTitle()} - Normal mode legend selection for highlighting:`,
        legendName
      )
      this.#highlightSelectedSegment(legendName)
      return
    }

    // For non-leaf selections or collapsed state, simulate chart click
    const fakeClickedPoint = {
      label: legendName,
      parent: 'Total',
    }

    console.log(
      `${this.chartTitle()} - Normal mode legend click simulating chart click for:`,
      legendName
    )
    this.#handleNormalModeClick(fakeClickedPoint, chartData)
  }

  public onChartRelayout(event: any): void {
    // Handle chart relayout events to ensure proper responsive behavior
    try {
      console.log('Chart relayout event:', event)
      // Force a resize if needed
      setTimeout(() => {
        if (
          this.isBrowser &&
          typeof window !== 'undefined' &&
          (window as any).Plotly
        ) {
          const plotlyElement = document.querySelector(
            'plotly-plot div[id^="plotly-div-"]'
          )
          if (plotlyElement) {
            ;(window as any).Plotly.Plots.resize(plotlyElement)
          }
        }
      }, 100)
    } catch (error) {
      console.error('Error handling chart relayout:', error)
    }
  }

  public onChartRestyle(event: any): void {
    // Handle chart restyle events
    try {
      console.log('Chart restyle event:', event)
    } catch (error) {
      console.error('Error handling chart restyle:', error)
    }
  }

  #updateChartHoleSize(): void {
    try {
      // Update the hole size based on chart-specific expanded state
      const holeSize = this.chartIsExpanded() ? 0.4 : 0.5

      const graph = this.graph()
      if (graph && graph.data && graph.data[0]) {
        // Use smooth animation for hole size changes
        if (
          this.isBrowser &&
          typeof window !== 'undefined' &&
          (window as any).Plotly
        ) {
          const plotlyElement = document.querySelector(
            'plotly-plot div[id^="plotly-div-"]'
          )
          if (plotlyElement) {
            // Animate the hole size change smoothly
            ;(window as any).Plotly.restyle(
              plotlyElement,
              {
                hole: holeSize,
              },
              [0],
              {
                transition: {
                  duration: 300,
                  easing: 'cubic-in-out',
                },
              }
            )
          }
        } else {
          // Fallback for when Plotly is not available - update the signal
          const currentData = [...graph.data]
          currentData[0] = { ...currentData[0], hole: holeSize }
          this.chartDataSignal.set(currentData)
          this.#forceChartRedraw()
        }
      }
    } catch (error) {
      console.error('Error updating chart hole size:', error)
    }
  }

  public goBack(): void {
    if (this.isFocusedMode() && this.chartCanGoBack()) {
      const chartType = this.chartTitle()
      this.aiFacade.drillBackToPreviousLevel(chartType)

      // Update table data
      const chartData = this.currentChartData()
      const formatted = formatParentData(chartData)
      if (formatted && Array.isArray(formatted)) {
        this.aiFacade.updateChartTableData(chartType, formatted)
      }

      // Update hole size after state change
      setTimeout(() => {
        this.#updateChartHoleSize()
      }, 50)
    }
  }

  #transformEcaDocumentTypes(ecaDocumentTypes: any[]): any[] {
    try {
      if (!ecaDocumentTypes || !Array.isArray(ecaDocumentTypes)) {
        console.warn('Invalid ECA document types data for transformation')
        return []
      }

      return ecaDocumentTypes
        .map((docType) => {
          if (!docType) return null

          return {
            id: docType.docTypeId || 0,
            category: docType.docTypeName || 'Unknown',
            name: docType.docTypeName || 'Unknown',
            count: docType.docCount || 0,
            percentage: docType.percentage || 0,
            subcategories: docType.children
              ? docType.children.map((child: any) => ({
                  subcategory: child?.docTypeName || 'Unknown',
                  count: child?.docCount || 0,
                  percentage: child?.percentage || 0,
                }))
              : [],
          }
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA document types:', error)
      return []
    }
  }

  #transformEcaTopicsToDocumentTypes(ecaTopics: any[]): any[] {
    try {
      if (!ecaTopics || !Array.isArray(ecaTopics)) {
        console.warn('Invalid ECA topics data for transformation')
        return []
      }

      return ecaTopics
        .map((topic) => {
          if (!topic) return null

          return {
            id: topic.topicId || 0,
            category: topic.topicName || 'Unknown',
            name: topic.topicName || 'Unknown',
            count: topic.docCount || 0,
            percentage: topic.percentage || 0,
            subcategories: topic.children
              ? topic.children.map((child: any) => ({
                  subcategory: child?.topicName || 'Unknown',
                  count: child?.docCount || 0,
                  percentage: child?.percentage || 0,
                }))
              : [],
          }
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA topics:', error)
      return []
    }
  }

  #transformEcaRelevanceData(ecaRelevanceData: any[]): any[] {
    try {
      if (!ecaRelevanceData || !Array.isArray(ecaRelevanceData)) {
        console.warn('Invalid ECA relevance data for transformation')
        return []
      }

      // Calculate total for percentage calculation
      const total = ecaRelevanceData.reduce(
        (sum, item) => sum + (item?.docCount || 0),
        0
      )

      return ecaRelevanceData
        .map((relevanceItem, index) => {
          if (!relevanceItem) return null

          const count = relevanceItem.docCount || 0
          const percentage = total > 0 ? (count / total) * 100 : 0

          return {
            id: index + 1, // Use index + 1 as ID like other charts
            category: relevanceItem.relevanceType || 'Unknown',
            name: relevanceItem.relevanceType || 'Unknown',
            count: count,
            percentage: percentage,
            // For relevance, no subcategories - it's a single layer structure
            subcategories: [],
          }
        })
        .filter((item) => item !== null && item.count > 0)
    } catch (error) {
      console.error('Error transforming ECA relevance data:', error)
      return []
    }
  }

  #initializeRelevanceChart(relevanceData: any[]): void {
    try {
      // For relevance data, create a simple single-layer structure
      const labels = ['Total', ...relevanceData.map((item) => item.category)]
      const parents = ['', ...relevanceData.map(() => 'Total')]
      const values = [0, ...relevanceData.map((item) => item.count)]

      this.labels = labels
      this.parents = parents

      // Store original values for smooth expansion/collapse
      this.originalValues = [...values]

      // Get chart colors based on chart type
      const chartColors = this.chartColors()
      const holeSize = 0.5

      // Update chart data signal
      this.chartDataSignal.set([
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: values,
          hole: holeSize,
          textinfo: 'label',
          marker: {
            colors: chartColors,
            line: { width: 0.5, color: '#ffffff' },
          },
          leaf: { opacity: 0.9 },
        },
      ])

      // Update chart layout signal
      this.chartLayoutSignal.set({
        autosize: true,
        automargin: false,
        branchvalues: 'remainder',
        margin: { t: 20, r: 20, b: 20, l: 20 },
        height: 400,
        width: undefined,
        showlegend: false,
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
      })

      // Update chart config signal
      this.chartConfigSignal.set({
        responsive: true,
        displayModeBar: false,
        displaylogo: false,
        autosizable: true,
        fillFrame: false,
        frameMargins: 0,
        // Enable interactive cursor
        staticPlot: false,
        scrollZoom: false,
        doubleClick: false,
        showTips: false,
        editable: false,
      })
    } catch (error) {
      console.error('Error initializing relevance chart:', error)
      this.#initializeEmptyChart()
    }
  }
}
