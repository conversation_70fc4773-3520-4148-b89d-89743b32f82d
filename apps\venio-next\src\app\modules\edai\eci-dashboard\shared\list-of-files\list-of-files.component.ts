import { Component, inject, computed } from '@angular/core'
import { GridModule } from '@progress/kendo-angular-grid'
import { UiPaginationModule, PageArgs } from '@venio/ui/pagination'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'

@Component({
  selector: 'venio-list-of-files',
  standalone: true,
  imports: [GridModule, UiPaginationModule],
  templateUrl: './list-of-files.component.html',
  styleUrls: ['./list-of-files.component.scss'],
})
export class ListOfFilesComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly files = toSignal(this.aiFacade.selectEciFileData$, {
    initialValue: [],
  })

  public readonly pagedFiles = toSignal(this.aiFacade.selectEciPagedFileData$, {
    initialValue: [],
  })

  public readonly paginationState = toSignal(
    this.aiFacade.selectEciPaginationState$,
    { initialValue: { currentPage: 1, pageSize: 15, totalRecords: 0 } }
  )

  // Signal selectors for active chart type and selected node data
  public readonly activeChartType = toSignal(
    this.aiFacade.selectActiveChartType$,
    { initialValue: null }
  )

  // Chart-specific selected node signals
  public readonly documentTypesSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(SunburstChartType.DocumentTypes),
    { initialValue: null }
  )

  public readonly relevantDocumentsSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(SunburstChartType.RelevantDocuments),
    { initialValue: null }
  )

  public readonly notRelevantDocumentsSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(
      SunburstChartType.NotRelevantDocuments
    ),
    { initialValue: null }
  )

  public readonly relevanceSelectedNode = toSignal(
    this.aiFacade.selectChartSelectedNode$(SunburstChartType.Relevance),
    { initialValue: null }
  )

  // Computed signal for selected node data based on active chart type
  public readonly selectedNodeData = computed(() => {
    const chartType = this.activeChartType()
    if (chartType) {
      let selectedNode = null
      switch (chartType) {
        case SunburstChartType.DocumentTypes:
          selectedNode = this.documentTypesSelectedNode()
          break
        case SunburstChartType.RelevantDocuments:
          selectedNode = this.relevantDocumentsSelectedNode()
          break
        case SunburstChartType.NotRelevantDocuments:
          selectedNode = this.notRelevantDocumentsSelectedNode()
          break
        case SunburstChartType.Relevance:
          selectedNode = this.relevanceSelectedNode()
          break
      }

      console.log('List of Files - Active Chart Type:', chartType)
      console.log('List of Files - Selected Node Data:', selectedNode)

      return selectedNode
    }
    return null
  })

  // Computed signal to determine if file list should be visible
  public readonly shouldShowFileList = computed(() => {
    const selectedNode = this.selectedNodeData()
    const hasSelection = selectedNode !== null && selectedNode !== undefined

    console.log('List of Files - Should Show:', hasSelection)

    return hasSelection
  })

  public pageChanged(event: PageArgs): void {
    this.aiFacade.updateEciPagination({ currentPage: event.pageNumber })
  }

  public pageSizeChanged(event: PageArgs): void {
    this.aiFacade.updateEciPagination({
      currentPage: event.pageNumber,
      pageSize: event.pageSize,
    })
  }
}
