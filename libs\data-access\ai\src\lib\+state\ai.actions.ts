import { createAction, props } from '@ngrx/store'
import { AIState } from './ai.reducer'
import {
  AIJobType,
  AiSearchPayload,
  AiSearchResult,
  AiSearchUiTypes,
  EdaiStatusFilterCriteria,
  JobRequestModel,
} from '../models/interfaces/ai.model'
import {
  CustodianModel,
  DocumentTypeModel,
  TableData,
  FileDataModel,
  WordCloudData,
  RelevanceChartData,
  BarChartData,
  PaginationState,
  EciDashboardFilters,
  ECADashboardRequestModel,
  SunburstChartType,
} from '../models/interfaces/eci-dashboard.model'
import { WebSocketMessage } from '@venio/data-access/common'
import { ResponseModel } from '@venio/shared/models/interfaces'

export enum AIActionTypes {
  // Resetting Project State
  ResetAiState = '[AI] Reset State',

  // Performing AI Search
  PerformAiSearch = '[AI] Perform AI Search',
  PerformAiSearchSuccess = '[AI] Perform AI Search Success',
  PerformAiSearchFailure = '[AI] Perform AI Search Failure',

  // Perform AI progress message update
  PerformAiProgressMessageUpdate = '[AI] Perform AI Progress Message Update',

  // Set selected document summary
  SetSelectedDocumentSummary = '[AI] Set Selected Document Summary',

  // Update AI UI selected tab
  UpdateSelectedSearchTab = '[AI] Update Selected Search Tab',

  // Trigger AI search reset
  ResetAiSearch = '[AI] Reset AI Search',

  // eDiscovery AI state types
  CreateJobEdai = '[eDiscovery AI] Create Job eDiscovery AI',
  CreateJobEdaiSuccess = '[eDiscovery AI] Create Job eDiscovery AI Success',
  CreateJobEdaiFailure = '[eDiscovery AI] Create Job eDiscovery AI Failure',

  // eDiscovery AI process results
  FetchEdaiStatus = '[eDiscovery AI] Fetch eDiscovery AI Status',
  FetchEdaiStatusSuccess = '[eDiscovery AI] Fetch eDiscovery AI Status Success',
  FetchEdaiStatusFailure = '[eDiscovery AI] Fetch eDiscovery AI Status Failure',

  FetchRelevanceCompletedJobs = '[eDiscovery AI] Fetch Relevance Completed Jobs',
  FetchRelevanceCompletedJobsSuccess = '[eDiscovery AI] Fetch Relevance Completed Jobs Success',
  FetchRelevanceCompletedJobsFailure = '[eDiscovery AI] Fetch Relevance Completed Jobs Failure',

  // eDiscovery AI document relevancy results
  FetchEdaiDocumentRelevancy = '[eDiscovery AI] Fetch eDiscovery AI Document Relevancy',
  FetchEdaiDocumentRelevancySuccess = '[eDiscovery AI] Fetch eDiscovery AI Document Relevancy Success',
  FetchEdaiDocumentRelevancyFailure = '[eDiscovery AI] Fetch eDiscovery AI Document Relevancy Failure',

  // eDiscovery AI document privilege results
  FetchEdaiDocumentPrivilege = '[eDiscovery AI] Fetch eDiscovery AI Document Privilege',
  FetchEdaiDocumentPrivilegeSuccess = '[eDiscovery AI] Fetch eDiscovery AI Document Privilege Success',
  FetchEdaiDocumentPrivilegeFailure = '[eDiscovery AI] Fetch eDiscovery AI Document Privilege Failure',

  // eDiscovery AI document job status details
  FetchEdaiJobStatus = '[eDiscovery AI] Fetch eDiscovery AI Job Status',
  FetchEdaiJobStatusSuccess = '[eDiscovery AI] Fetch eDiscovery AI Job Status Success',
  FetchEdaiJobStatusFailure = '[eDiscovery AI] Fetch eDiscovery AI Job Status Failure',

  // eDiscovery AI PII template results
  FetchEdaiPiiTemplate = '[eDiscovery AI] Fetch eDiscovery AI PII Template',
  FetchEdaiPiiTemplateSuccess = '[eDiscovery AI] Fetch eDiscovery AI PII Template Success',
  FetchEdaiPiiTemplateFailure = '[eDiscovery AI] Fetch eDiscovery AI PII Template Failure',

  // eDiscovery AI PII entities results
  FetchEdaiPiiEntities = '[eDiscovery AI] Fetch eDiscovery AI PII Entities',
  FetchEdaiPiiEntitiesSuccess = '[eDiscovery AI] Fetch eDiscovery AI PII Entities Success',
  FetchEdaiPiiEntitiesFailure = '[eDiscovery AI] Fetch eDiscovery AI PII Entities Failure',

  // eDiscovery AI document PII  results
  FetchEdaiDocumentPii = '[eDiscovery AI] Fetch eDiscovery AI Document PII',
  FetchEdaiDocumentPiiSuccess = '[eDiscovery AI] Fetch eDiscovery AI Document PII Success',
  FetchEdaiDocumentPiiFailure = '[eDiscovery AI] Fetch eDiscovery AI Document PII Failure',

  // ECI Dashboard Actions
  SetEciFocusedSectionOpened = '[ECI Dashboard] Set Focused Section Opened',
  SetEciParentDataView = '[ECI Dashboard] Set Parent Data View',
  SetEciShowDetails = '[ECI Dashboard] Set Show Details',
  SetEciFilterPopupVisibility = '[ECI Dashboard] Set Filter Popup Visibility',
  SetEciCustodianFiltersVisibility = '[ECI Dashboard] Set Custodian Filters Visibility',
  FetchEciDashboardData = '[ECI Dashboard] Fetch Dashboard Data',
  FetchEciDashboardDataSuccess = '[ECI Dashboard] Fetch Dashboard Data Success',
  FetchEciDashboardDataFailure = '[ECI Dashboard] Fetch Dashboard Data Failure',
  StoreEciTableData = '[ECI Dashboard] Store Table Data',
  StoreEciSelectedDocuments = '[ECI Dashboard] Store Selected Documents',
  HandleEciSunburstClick = '[ECI Dashboard] Handle Sunburst Click',
  UpdateEciPagination = '[ECI Dashboard] Update Pagination',
  ResetEciDashboardState = '[ECI Dashboard] Reset Dashboard State',
  ResetEciFocusedSectionState = '[ECI Dashboard] Reset Focused Section State',

  // Chart-specific drill-down actions
  SetActiveChartType = '[ECI Dashboard] Set Active Chart Type',
  InitializeChartDrillDown = '[ECI Dashboard] Initialize Chart Drill Down',
  DrillDownToNextLevel = '[ECI Dashboard] Drill Down To Next Level',
  DrillBackToPreviousLevel = '[ECI Dashboard] Drill Back To Previous Level',
  ResetChartDrillDown = '[ECI Dashboard] Reset Chart Drill Down',
  UpdateChartTableData = '[ECI Dashboard] Update Chart Table Data',
  SetChartSelectedNode = '[ECI Dashboard] Set Chart Selected Node',

  // ECA API Actions
  FetchEcaRelevance = '[ECA] Fetch ECA Relevance',
  FetchEcaRelevanceSuccess = '[ECA] Fetch ECA Relevance Success',
  FetchEcaRelevanceFailure = '[ECA] Fetch ECA Relevance Failure',
  FetchEcaDocumentTypes = '[ECA] Fetch ECA Document Types',
  FetchEcaDocumentTypesSuccess = '[ECA] Fetch ECA Document Types Success',
  FetchEcaDocumentTypesFailure = '[ECA] Fetch ECA Document Types Failure',
  FetchEcaTopicsRelevant = '[ECA] Fetch ECA Topics Relevant',
  FetchEcaTopicsRelevantSuccess = '[ECA] Fetch ECA Topics Relevant Success',
  FetchEcaTopicsRelevantFailure = '[ECA] Fetch ECA Topics Relevant Failure',
  FetchEcaTopicsNonRelevant = '[ECA] Fetch ECA Topics Non-Relevant',
  FetchEcaTopicsNonRelevantSuccess = '[ECA] Fetch ECA Topics Non-Relevant Success',
  FetchEcaTopicsNonRelevantFailure = '[ECA] Fetch ECA Topics Non-Relevant Failure',
  FetchEcaWordCloud = '[ECA] Fetch ECA Word Cloud',
  FetchEcaWordCloudSuccess = '[ECA] Fetch ECA Word Cloud Success',
  FetchEcaWordCloudFailure = '[ECA] Fetch ECA Word Cloud Failure',
  FetchEcaInappropriateContent = '[ECA] Fetch ECA Inappropriate Content',
  FetchEcaInappropriateContentSuccess = '[ECA] Fetch ECA Inappropriate Content Success',
  FetchEcaInappropriateContentFailure = '[ECA] Fetch ECA Inappropriate Content Failure',
  DownloadCSV = '[ECA] Download CSV',
}

export const resetAiState = createAction(
  AIActionTypes.ResetAiState,
  props<{ stateKey: keyof AIState | Array<keyof AIState> }>()
)

export const performAiSearch = createAction(
  AIActionTypes.PerformAiSearch,
  props<{ payload: AiSearchPayload; clientId: string; uuid: string }>()
)

export const performAiSearchSuccess = createAction(
  AIActionTypes.PerformAiSearchSuccess,
  props<{ aiSearchSuccess: AiSearchResult; uuid: string }>()
)

export const performAiSearchFailure = createAction(
  AIActionTypes.PerformAiSearchFailure,
  props<{ aiSearchError: any; uuid: string }>()
)

export const performAiProgressMessageUpdate = createAction(
  AIActionTypes.PerformAiProgressMessageUpdate,
  props<{ progressMessage: WebSocketMessage; uuid: string }>()
)

export const setSelectedDocumentSummary = createAction(
  AIActionTypes.SetSelectedDocumentSummary,
  props<{ selectedDocumentSummary: AiSearchResult }>()
)

export const updateSelectedSearchTab = createAction(
  AIActionTypes.UpdateSelectedSearchTab,
  props<{ selectedSearchTab: AiSearchUiTypes }>()
)

export const resetTrigger = createAction(
  AIActionTypes.ResetAiSearch,
  props<{ isResetTriggered: boolean }>()
)

export const createJobEdai = createAction(
  AIActionTypes.CreateJobEdai,
  props<{ projectId: number; payload: JobRequestModel }>()
)

export const createJobEdaiSuccess = createAction(
  AIActionTypes.CreateJobEdaiSuccess,
  props<{
    createJobEdaiSuccess: ResponseModel
  }>()
)

export const createJobEdaiFailure = createAction(
  AIActionTypes.CreateJobEdaiFailure,
  props<{ createJobEdaiError: ResponseModel }>()
)

export const fetchEdaiRelevanceCompletedJobs = createAction(
  AIActionTypes.FetchRelevanceCompletedJobs,
  props<{ projectId: number }>()
)
export const fetchEdaiRelevanceCompletedJobsSuccess = createAction(
  AIActionTypes.FetchRelevanceCompletedJobsSuccess,
  props<{ edaiStatus: any }>()
)

export const fetchEdaiRelevanceCompletedJobsFailure = createAction(
  AIActionTypes.FetchRelevanceCompletedJobsFailure,
  props<{ edaiStatusError: any }>()
)

export const fetchEdaiStatus = createAction(
  AIActionTypes.FetchEdaiStatus,
  props<{ projectId: number; filterCriteria: EdaiStatusFilterCriteria }>()
)

export const fetchEdaiStatusSuccess = createAction(
  AIActionTypes.FetchEdaiStatusSuccess,
  props<{ edaiStatus: any }>()
)

export const fetchEdaiStatusFailure = createAction(
  AIActionTypes.FetchEdaiStatusFailure,
  props<{ edaiStatusError: any }>()
)

export const fetchEdaiDocumentRelevancy = createAction(
  AIActionTypes.FetchEdaiDocumentRelevancy,
  props<{ projectId: number; fileId: number }>()
)

export const fetchEdaiDocumentRelevancySuccess = createAction(
  AIActionTypes.FetchEdaiDocumentRelevancySuccess,
  props<{ edaiDocumentRelevancySuccess: ResponseModel }>()
)

export const fetchEdaiDocumentRelevancyFailure = createAction(
  AIActionTypes.FetchEdaiDocumentRelevancyFailure,
  props<{ edaiDocumentRelevancyError: ResponseModel }>()
)

export const fetchJobStatusDetails = createAction(
  AIActionTypes.FetchEdaiJobStatus,
  props<{ projectId: number; jobId: number; jobType: AIJobType }>()
)

export const fetchJobStatusDetailsSuccess = createAction(
  AIActionTypes.FetchEdaiJobStatusSuccess,
  props<{ edaiJobStatusDetailsSuccess: ResponseModel }>()
)

export const fetchJobStatusDetailsFailure = createAction(
  AIActionTypes.FetchEdaiJobStatusFailure,
  props<{ edaiJobStatusDetailsError: ResponseModel }>()
)

export const fetchEdaiDocumentPrivilege = createAction(
  AIActionTypes.FetchEdaiDocumentPrivilege,
  props<{ projectId: number; fileId: number }>()
)

export const fetchEdaiDocumentPrivilegeSuccess = createAction(
  AIActionTypes.FetchEdaiDocumentPrivilegeSuccess,
  props<{ edaiDocumentPrivilegeSuccess: ResponseModel }>()
)

export const fetchEdaiDocumentPrivilegeFailure = createAction(
  AIActionTypes.FetchEdaiDocumentPrivilegeFailure,
  props<{ edaiDocumentPrivilegeError: ResponseModel }>()
)

export const fetchEdaiPiiTemplate = createAction(
  AIActionTypes.FetchEdaiPiiTemplate,
  props<{ projectId: number }>()
)

export const fetchEdaiPiiTemplateSuccess = createAction(
  AIActionTypes.FetchEdaiPiiTemplateSuccess,
  props<{ edaiPIITemplateSuccess: ResponseModel }>()
)

export const fetchEdaiPiiTemplateFailure = createAction(
  AIActionTypes.FetchEdaiPiiTemplateFailure,
  props<{ edaiPIITemplateError: ResponseModel }>()
)

export const fetchEdaiPiiEntities = createAction(
  AIActionTypes.FetchEdaiPiiEntities,
  props<{ projectId: number }>()
)

export const fetchEdaiPiiEntitiesSuccess = createAction(
  AIActionTypes.FetchEdaiPiiEntitiesSuccess,
  props<{ edaiPIIEntitySuccess: ResponseModel }>()
)

export const fetchEdaiPiiEntitiesFailure = createAction(
  AIActionTypes.FetchEdaiPiiEntitiesFailure,
  props<{ edaiPIIEntityError: ResponseModel }>()
)

export const fetchEdaiDocumentPii = createAction(
  AIActionTypes.FetchEdaiDocumentPii,
  props<{ projectId: number; fileId: number; isPiiDetect: boolean }>()
)

export const fetchEdaiDocumentPiiSuccess = createAction(
  AIActionTypes.FetchEdaiDocumentPiiSuccess,
  props<{ edaiDocumentPIISuccess: ResponseModel; isPiiDetect: boolean }>()
)

export const fetchEdaiDocumentPiiFailure = createAction(
  AIActionTypes.FetchEdaiDocumentPiiFailure,
  props<{ edaiDocumentPIIError: ResponseModel; isPiiDetect: boolean }>()
)

// ECI Dashboard Action Creators
export const setEciFocusedSectionOpened = createAction(
  AIActionTypes.SetEciFocusedSectionOpened,
  props<{ isOpened: boolean }>()
)

export const setEciParentDataView = createAction(
  AIActionTypes.SetEciParentDataView,
  props<{ isParentData: boolean }>()
)

export const setEciShowDetails = createAction(
  AIActionTypes.SetEciShowDetails,
  props<{ showDetails: boolean }>()
)

export const setEciFilterPopupVisibility = createAction(
  AIActionTypes.SetEciFilterPopupVisibility,
  props<{ isVisible: boolean }>()
)

export const setEciCustodianFiltersVisibility = createAction(
  AIActionTypes.SetEciCustodianFiltersVisibility,
  props<{ isVisible: boolean }>()
)

export const fetchEciDashboardData = createAction(
  AIActionTypes.FetchEciDashboardData,
  props<{
    projectId: number
    jobId?: number
    filters?: EciDashboardFilters
  }>()
)

export const fetchEciDashboardDataSuccess = createAction(
  AIActionTypes.FetchEciDashboardDataSuccess,
  props<{
    custodians: CustodianModel[]
    documentTypes: DocumentTypeModel[]
    fileData: FileDataModel[]
    wordCloudData: WordCloudData[]
    relevanceData: RelevanceChartData
    barChartData: BarChartData
  }>()
)

export const fetchEciDashboardDataFailure = createAction(
  AIActionTypes.FetchEciDashboardDataFailure,
  props<{ error: any }>()
)

export const storeEciTableData = createAction(
  AIActionTypes.StoreEciTableData,
  props<{ tableData: TableData[] }>()
)

export const storeEciSelectedDocuments = createAction(
  AIActionTypes.StoreEciSelectedDocuments,
  props<{ selectedDocuments: number }>()
)

export const handleEciSunburstClick = createAction(
  AIActionTypes.HandleEciSunburstClick,
  props<{ clickData: any; isParentData: boolean }>()
)

export const updateEciPagination = createAction(
  AIActionTypes.UpdateEciPagination,
  props<{ paginationState: Partial<PaginationState> }>()
)

export const resetEciDashboardState = createAction(
  AIActionTypes.ResetEciDashboardState
)

// Chart-specific drill-down action creators
export const setActiveChartType = createAction(
  AIActionTypes.SetActiveChartType,
  props<{ chartType: SunburstChartType }>()
)

export const initializeChartDrillDown = createAction(
  AIActionTypes.InitializeChartDrillDown,
  props<{ chartType: SunburstChartType; initialData: any[] }>()
)

export const drillDownToNextLevel = createAction(
  AIActionTypes.DrillDownToNextLevel,
  props<{
    chartType: SunburstChartType
    selectedNode: any
    nextLevelData: any[]
  }>()
)

export const drillBackToPreviousLevel = createAction(
  AIActionTypes.DrillBackToPreviousLevel,
  props<{ chartType: SunburstChartType }>()
)

export const resetChartDrillDown = createAction(
  AIActionTypes.ResetChartDrillDown,
  props<{ chartType: SunburstChartType }>()
)

export const updateChartTableData = createAction(
  AIActionTypes.UpdateChartTableData,
  props<{ chartType: SunburstChartType; tableData: TableData[] }>()
)

export const setChartSelectedNode = createAction(
  AIActionTypes.SetChartSelectedNode,
  props<{ chartType: SunburstChartType; selectedNode: any }>()
)

// ECA API Actions
export const fetchEcaRelevance = createAction(
  AIActionTypes.FetchEcaRelevance,
  props<{ projectId: number; requestModel: ECADashboardRequestModel }>()
)

export const fetchEcaRelevanceSuccess = createAction(
  AIActionTypes.FetchEcaRelevanceSuccess,
  props<{ ecaRelevanceSuccess: ResponseModel }>()
)

export const fetchEcaRelevanceFailure = createAction(
  AIActionTypes.FetchEcaRelevanceFailure,
  props<{ ecaRelevanceError: ResponseModel }>()
)

export const fetchEcaDocumentTypes = createAction(
  AIActionTypes.FetchEcaDocumentTypes,
  props<{ projectId: number; requestModel: ECADashboardRequestModel }>()
)

export const fetchEcaDocumentTypesSuccess = createAction(
  AIActionTypes.FetchEcaDocumentTypesSuccess,
  props<{ ecaDocumentTypesSuccess: ResponseModel }>()
)

export const fetchEcaDocumentTypesFailure = createAction(
  AIActionTypes.FetchEcaDocumentTypesFailure,
  props<{ ecaDocumentTypesError: ResponseModel }>()
)

export const fetchEcaTopicsRelevant = createAction(
  AIActionTypes.FetchEcaTopicsRelevant,
  props<{ projectId: number; requestModel: ECADashboardRequestModel }>()
)

export const fetchEcaTopicsRelevantSuccess = createAction(
  AIActionTypes.FetchEcaTopicsRelevantSuccess,
  props<{ ecaTopicsRelevantSuccess: ResponseModel }>()
)

export const fetchEcaTopicsRelevantFailure = createAction(
  AIActionTypes.FetchEcaTopicsRelevantFailure,
  props<{ ecaTopicsRelevantError: ResponseModel }>()
)

export const fetchEcaTopicsNonRelevant = createAction(
  AIActionTypes.FetchEcaTopicsNonRelevant,
  props<{ projectId: number; requestModel: ECADashboardRequestModel }>()
)

export const fetchEcaTopicsNonRelevantSuccess = createAction(
  AIActionTypes.FetchEcaTopicsNonRelevantSuccess,
  props<{ ecaTopicsNonRelevantSuccess: ResponseModel }>()
)

export const fetchEcaTopicsNonRelevantFailure = createAction(
  AIActionTypes.FetchEcaTopicsNonRelevantFailure,
  props<{ ecaTopicsNonRelevantError: ResponseModel }>()
)
export const downloadCSV = createAction(
  AIActionTypes.DownloadCSV,
  props<{ projectId: number; requestModel: ECADashboardRequestModel }>()
)

export const downloadCSVSuccess = createAction('[AI] Download CSV Success')

export const downloadCSVFailure = createAction(
  '[AI] Download CSV Failure',
  props<{ downloadCSVError: ResponseModel }>()
)

// ECA Word Cloud Actions
export const fetchEcaWordCloud = createAction(
  AIActionTypes.FetchEcaWordCloud,
  props<{ projectId: number; requestModel: ECADashboardRequestModel }>()
)

export const fetchEcaWordCloudSuccess = createAction(
  AIActionTypes.FetchEcaWordCloudSuccess,
  props<{ ecaWordCloudSuccess: ResponseModel }>()
)

export const fetchEcaWordCloudFailure = createAction(
  AIActionTypes.FetchEcaWordCloudFailure,
  props<{ ecaWordCloudError: ResponseModel }>()
)

// ECA Inappropriate Content Actions
export const fetchEcaInappropriateContent = createAction(
  AIActionTypes.FetchEcaInappropriateContent,
  props<{ projectId: number; requestModel: ECADashboardRequestModel }>()
)

export const fetchEcaInappropriateContentSuccess = createAction(
  AIActionTypes.FetchEcaInappropriateContentSuccess,
  props<{ ecaInappropriateContentSuccess: ResponseModel }>()
)

export const fetchEcaInappropriateContentFailure = createAction(
  AIActionTypes.FetchEcaInappropriateContentFailure,
  props<{ ecaInappropriateContentError: ResponseModel }>()
)
