<div
  class="wrapper-center t-absolute t-top-1/2 t-left-1/2 t-text-center t-z-10 t-bg-white t-flex t-justify-center t-items-center t-rounded-full t-flex-col"
  [class.expanded]="isExpanded()">
  <p
    class="center-title t-text-sm t-font-semibold t-text-primary t-m-0"
    [class.expanded-title]="isExpanded()">
    {{ centerText() }}
  </p>
  <div
    *ngIf="showViewDetails()"
    [ngClass]="{
      'view-details-container t-flex t-items-center t-justify-center t-cursor-pointer': true,
      't-text-secondary': centerText() === sunburstChartType.Relevance,
      't-text-[#ff00ff]': centerText() !== sunburstChartType.Relevance,
      'expanded-details': isExpanded()
    }"
    (click)="openFocusedSection()">
    <span
      class="view-details-text t-text-xs t-font-medium"
      [class.expanded-text]="isExpanded()"
      >View Details</span
    >
    <button
      kendoButton
      [svgIcon]="svgOpenNew"
      title="Open in new tab"
      size="none"
      fillMode="clear"
      [ngClass]="{
        'view-details-button': true,
        't-text-secondary': centerText() === sunburstChartType.Relevance,
        't-text-[#ff00ff]': centerText() !== sunburstChartType.Relevance,
        'expanded-button': isExpanded()
      }"></button>
  </div>

  <!-- Back button for legend-modified state -->
  <div *ngIf="showBackButton()" class="t-mt-2">
    <button
      kendoButton
      size="small"
      fillMode="outline"
      look="clear"
      themeColor="primary"
      (click)="onBackButtonClick()"
      class="t-text-xs t-px-2 t-py-1">
      ← Back
    </button>
  </div>
</div>
