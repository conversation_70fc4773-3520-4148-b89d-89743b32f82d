import { Component, computed, inject, PLATFORM_ID } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { CenterTextComponent } from '../center-text/center-text.component'
import { isPlatformBrowser, NgFor } from '@angular/common'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  AiFacade,
  ECADashboardType,
  SunburstChartType,
} from '@venio/data-access/ai'
import { relevanceChartColors } from '../../constants/colors'

@Component({
  selector: 'venio-relevance',
  standalone: true,
  imports: [
    PlotlyModule,
    TitleAndDownloadComponent,
    CenterTextComponent,
    NgFor,
    LoaderModule,
  ],
  templateUrl: './relevance.component.html',
  styleUrl: './relevance.component.scss',
})
export class RelevanceComponent {
  public readonly sunburstChartType = SunburstChartType

  public readonly dashboardType = ECADashboardType

  private readonly aiFacade = inject(AiFacade)

  private readonly platformId = inject(PLATFORM_ID)

  private readonly isBrowser = isPlatformBrowser(this.platformId)

  public readonly relevanceData = toSignal(
    this.aiFacade.selectEciRelevanceData$,
    { initialValue: null }
  )

  // ECA API data
  public readonly ecaRelevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  public readonly isEcaRelevanceLoading = toSignal(
    this.aiFacade.selectIsEcaRelevanceLoading$,
    { initialValue: false }
  )

  public readonly ecaRelevanceError = toSignal(
    this.aiFacade.selectEcaRelevanceError$,
    { initialValue: null }
  )

  public readonly sunburstChartColors = relevanceChartColors

  public getColor(i: number): string {
    return this.sunburstChartColors[i + 1]
  }

  // Computed signal for fully formatted chart data
  public readonly formattedChartData = computed(() => {
    try {
      // Use ECA API data if available, otherwise fallback to mock data
      const ecaData = this.ecaRelevanceData()
      let values: number[] = []
      let colors: string[] = []
      let labels: string[] = []

      if (ecaData?.data && Array.isArray(ecaData.data)) {
        // Process ECA API data
        const validData = ecaData.data.filter((item: any) => item?.docCount > 0)
        values = validData.map((item: any) => item.docCount)
        labels = validData.map((item: any) => item.relevanceType)

        if (values.length > 0) {
          colors = this.#generateColors(values.length)
        }
      } else {
        // Fallback to legacy data
        const data = this.relevanceData()
        values = data?.values || []
        colors = data?.colors || []
        labels = data?.labels || []
      }

      // Ensure we have valid data
      if (!values.length) {
        values = [1]
        colors = ['#e5e7eb']
        labels = ['No Data']
      }

      return {
        values,
        colors,
        labels,
        isValid: values.length > 0 && colors.length > 0 && labels.length > 0,
      }
    } catch (error) {
      console.error('Error formatting relevance chart data:', error)
      return {
        values: [1],
        colors: ['#e5e7eb'],
        labels: ['Error'],
        isValid: false,
      }
    }
  })

  // Signal to determine if chart data is ready for rendering
  public readonly isChartDataReady = computed(() => {
    const formattedData = this.formattedChartData()
    return formattedData.isValid && !this.isEcaRelevanceLoading()
  })

  // Signal to determine if chart should be rendered
  public readonly shouldRenderChart = computed(() => {
    return this.isBrowser && this.isChartDataReady()
  })

  // Computed signal for labels
  public readonly labels = computed(() => {
    const formattedData = this.formattedChartData()
    return formattedData.labels
  })

  // Computed signal for fully formatted graph data
  public readonly graph = computed(() => {
    const formattedData = this.formattedChartData()

    return {
      data: [
        {
          values: formattedData.values,
          type: 'pie',
          hole: 0.5,
          marker: { colors: formattedData.colors },
          textinfo: 'none',
        },
      ],
      layout: {
        autosize: true,
        title: '',
        automargin: false,
        margin: { t: 20, r: 20, b: 20, l: 20 },
        height: 400,
        width: undefined,
        showlegend: false,
        plot_bgcolor: 'rgba(0,0,0,0)',
        paper_bgcolor: 'rgba(0,0,0,0)',
      },
    }
  })

  #generateColors(count: number): string[] {
    // Use the same color scheme as the legend (excluding 'transparent' at index 0)
    const chartColors = this.sunburstChartColors.slice(1)

    // Repeat colors if we need more than the available set
    const colors: string[] = []
    for (let i = 0; i < count; i++) {
      colors.push(chartColors[i % chartColors.length])
    }
    return colors
  }

  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d',
    ],
    // Ensure proper resizing behavior
    autosizable: true,
    fillFrame: false,
    frameMargins: 0,
  }
}
